/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

#ifndef H_BLE_LL_CRYPTO_
#define H_BLE_LL_CRYPTO_

#ifdef __cplusplus
extern "C" {
#endif

int
ble_ll_crypto_cmac(const uint8_t *key, const uint8_t *in, int len,
                   uint8_t *out);

static inline int
ble_ll_crypto_h6(const uint8_t *w, const uint8_t *key_id, uint8_t *out)
{
    return ble_ll_crypto_cmac(w, key_id, 4, out);
}

static inline int
ble_ll_crypto_h7(const uint8_t *salt, const uint8_t *w, uint8_t *out)
{
    return ble_ll_crypto_cmac(salt, w, 16, out);
}

int ble_ll_crypto_h8(const uint8_t *k, const uint8_t *s, const uint8_t *key_id,
                     uint8_t *out);

#ifdef __cplusplus
}
#endif

#endif /* H_BLE_LL_CRYPTO_ */
