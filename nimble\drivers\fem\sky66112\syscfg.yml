# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

syscfg.defs:
    SKY66112_PIN_CSD:
        description: >
            GPIO pin number to control CSD signal.
            When set to '-1', pin state will not be changed and it should be
            driven externally.
        value: -1
    SKY66112_PIN_CPS:
        description: >
            GPIO pin number to control CPS signal.
            When set to '-1', pin state will not be changed and it should be
            driven externally.
        value: -1
    SKY66112_PIN_CHL:
        description: >
            GPIO pin number to control CHL signal.
            When set to '-1', pin state will not be changed and it should be
            driven externally.
        value: -1
    SKY66112_PIN_SEL:
        description: >
            GPIO pin number to control SEL signal.
            When set to '-1', pin state will not be changed and it should be
            driven externally.
        value: -1
    SKY66112_TX_HP_MODE:
        description: >
            Enables high-power mode for TX.
            Only valid if CHL signal is controller by driver.
        value: 0
    SKY66112_TX_BYPASS:
        description: >
            Enables bypass for TX which effectively disables operation as PA.
            Only valid if CPS signal is controller by driver.
        value: 0
    SKY66112_RX_BYPASS:
        description: >
            Enables bypass for RX which effectively disables operation as PA.
            Only valid if CPS signal is controller by driver.
        value: 0
    SKY66112_ANTENNA_PORT:
        description: >
            Selects which antenna port should be enabled. If 'runtime' is
            selected sky66112_default_antenna_get() (see sky66112/sky66112.h)
            shall be implemeneted by application.
        choices:
            - ANT1
            - ANT2
            - runtime
        value: ANT1

syscfg.vals.!BLE_FEM_PA:
    # Enable TX bypass by default if PA is disabled
    SKY66112_TX_BYPASS: 1

syscfg.vals.!BLE_FEM_LNA:
    # Enable RX bypass by default if LNA is disabled
    SKY66112_RX_BYPASS: 1
