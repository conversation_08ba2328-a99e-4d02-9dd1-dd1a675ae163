#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

syscfg.vals:
    MCU_DEEP_SLEEP: 1
    MCU_SLP_TIMER: 1
    MCU_SLP_TIMER_32K_ONLY: 1
    MCU_DEBUG_HCI_EVENT_ON_FAULT: 1
    MCU_DEBUG_HCI_EVENT_ON_ASSERT: 1
    MSYS_1_BLOCK_SIZE: 308

    BLE_TRANSPORT_HS: dialog_cmac
    BLE_TRANSPORT_ACL_COUNT: 16
    BLE_TRANSPORT_ACL_SIZE: 255
    BLE_TRANSPORT_EVT_COUNT: 4
    BLE_TRANSPORT_EVT_DISCARDABLE_COUNT: 16

    # LL recommended settings (decreasing timing values is not recommended)
    BLE_LL_STACK_SIZE: 200
    BLE_LL_CFG_FEAT_CTRL_TO_HOST_FLOW_CONTROL: 1
    BLE_LL_CONN_INIT_MIN_WIN_OFFSET: 2
    BLE_LL_RFMGMT_ENABLE_TIME: 20
    BLE_LL_SCHED_AUX_MAFS_DELAY: 150
    BLE_LL_SCHED_AUX_CHAIN_MAFS_DELAY: 150

    # NOTE: set public address in target settings
    # BLE_LL_PUBLIC_DEV_ADDR: 0xffffffffffff

    # LL features
    BLE_VERSION: 53
    BLE_EXT_ADV: 1
    BLE_EXT_ADV_MAX_SIZE: 1650
    BLE_PERIODIC_ADV: 1
    BLE_PERIODIC_ADV_SYNC_TRANSFER: 1
    BLE_MULTI_ADV_INSTANCES: 4
    BLE_MAX_PERIODIC_SYNCS: 4
    BLE_MAX_CONNECTIONS: 4
    BLE_PHY_2M: 1
    BLE_LL_CFG_FEAT_DATA_LEN_EXT: 1
    BLE_LL_CFG_FEAT_LL_PRIVACY: 1
    BLE_LL_CFG_FEAT_LL_SCA_UPDATE: 1
    BLE_LL_CFG_FEAT_LL_ENHANCED_CONN_UPDATE: 1
    BLE_LL_CONN_INIT_SLOTS: 1
    BLE_LL_SCAN_AUX_SEGMENT_CNT: 16
    BLE_LL_NUM_SCAN_DUP_ADVS: 64
