# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

syscfg.defs:
    BLE_SVC_BAS_BATTERY_LEVEL_READ_PERM:
        description: >
            Defines permissions for reading "Battery Level" characteristics. Can
            be zero to allow read without extra permissions or combination of:
                BLE_GATT_CHR_F_READ_ENC
                BLE_GATT_CHR_F_READ_AUTHEN
                BLE_GATT_CHR_F_READ_AUTHOR
        value: 0
    BLE_SVC_BAS_BATTERY_LEVEL_NOTIFY_ENABLE:
        description: >
            Set to 1 to support notification or 0 to disable it.
        value: 1
    BLE_SVC_BAS_SYSINIT_STAGE:
        description: >
            Sysinit stage for the battery level service.
        value: 303
