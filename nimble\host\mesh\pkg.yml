#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

pkg.name: nimble/host/mesh
pkg.description: Bluetooth Mesh
pkg.author: "Apache Mynewt <<EMAIL>>"
pkg.homepage: "http://mynewt.apache.org/"
pkg.keywords:
    - ble
    - bluetooth
    - mesh

pkg.deps:
    - "@apache-mynewt-core/kernel/os"
    - "@apache-mynewt-core/util/mem"
    - "@apache-mynewt-core/crypto/tinycrypt"
    - nimble
    - nimble/host

pkg.deps.BLE_MESH_SHELL:
    - "@apache-mynewt-core/sys/shell"

pkg.deps.BLE_MESH_SETTINGS:
    - "@apache-mynewt-core/encoding/base64"
    - "@apache-mynewt-core/sys/config"

pkg.req_apis:
    - log
    - stats

pkg.init:
    bt_mesh_register_gatt: 'MYNEWT_VAL(BLE_MESH_SYSINIT_STAGE)'
    ble_mesh_shell_init: 'MYNEWT_VAL(BLE_MESH_SYSINIT_STAGE_SHELL)'
