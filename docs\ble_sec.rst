NimBLE Security
---------------

The Bluetooth Low Energy security model includes five distinct security
concepts as listed below. For detailed specifications, see BLUETOOTH
SPECIFICATION Version 4.2 [Vol 1, Part A].

-  **Pairing**: The process for creating one or more shared secret keys.
   In LE a single link key is generated by combining contributions from
   each device into a link key used during pairing.

-  **Bonding**: The act of storing the keys created during pairing for
   use in subsequent connections in order to form a trusted device pair.

-  **Device authentication**: Verification that the two devices have the
   same keys (verify device identity)

-  **Encryption**: Keeps message confidential. Encryption in Bluetooth
   LE uses AES-CCM cryptography and is performed in the *Controller*.

-  **Message integrity**: Protects against message forgeries.

Bluetooth LE uses four association models depending on the I/O
capabilities of the devices.

-  **Just Works**: designed for scenarios where at least one of the
   devices does not have a display capable of displaying a six digit
   number nor does it have a keyboard capable of entering six decimal
   digits.

-  **Numeric Comparison**: designed for scenarios where both devices are
   capable of displaying a six digit number and both are capable of
   having the user enter "yes" or "no". A good example of this model is
   the cell phone / PC scenario.

-  **Out of Band**: designed for scenarios where an Out of Band
   mechanism is used to both discover the devices as well as to exchange
   or transfer cryptographic numbers used in the pairing process.

-  **Passkey Entry**: designed for the scenario where one device has
   input capability but does not have the capability to display six
   digits and the other device has output capabilities. A good example
   of this model is the PC and keyboard scenario.

Key Generation
~~~~~~~~~~~~~~

Key generation for all purposes in Bluetooth LE is performed by the
*Host* on each LE device independent of any other LE device.

Privacy Feature
~~~~~~~~~~~~~~~

Bluetooth LE supports an optional feature during connection mode and
connection procedures that reduces the ability to track a LE device over
a period of time by changing the Bluetooth device address on a frequent
basis.

There are two variants of the privacy feature.

-  In the first variant, private addresses are resolved and generated by
   the *Host*.
-  In the second variant, private addresses are resolved and generated
   by the *Controller* without involving the Host after the Host
   provides the Controller device identity information. The Host may
   provide the Controller with a complete resolving list or a subset of
   the resolving list. Device filtering becomes possible in the second
   variant when address resolution is performed in the Controller
   because the peer’s device identity address can be resolved prior to
   checking whether it is in the white list.

**Note**: When address resolution is performed exclusively in the Host,
a device may experience increased power consumption because device
filtering must be disabled. For more details on the privacy feature,
refer to BLUETOOTH SPECIFICATION Version 4.2 [Vol 3, Part C] (Published
02 December 2014), Page 592.
