# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

syscfg.defs:
    BABBLESIM: 1
    BSP_NRF52:
        description: 'Set to indicate that BSP has NRF52'
        value: 1
    SOFT_PWM:
        description: 'Enable soft PWM'
        value: 0
    ENC_FLASH_DEV:
        description: 'Encrypting flash driver over interal flash for testing'
        value: 0
    UARTBB_0:
        description: 'Enable bit-banger UART 0'
        value: 0
    RAM_RESIDENT:
        description: 'Compile app to be loaded to RAM'
        value: 0

syscfg.vals:
    HAL_SBRK: 0
    OS_TICKS_PER_SEC: 1024
    OS_MAIN_STACK_SIZE: 8000
    MCU_TIMER_POLLER_PRIO: 0
    BLE_LL_PRIO: 1
    MCU_UART_POLLER_PRIO: 9

    # Enable nRF52832 MCU
    MCU_TARGET: nRF52832
    # Set default pins for peripherals
    UART_0_PIN_TX: 6
    UART_0_PIN_RX: 8
    UART_0_PIN_RTS: 5
    UART_0_PIN_CTS: 7
    SPI_0_MASTER_PIN_SCK: 23
    SPI_0_MASTER_PIN_MOSI: 24
    SPI_0_MASTER_PIN_MISO: 25
    SPI_0_SLAVE_PIN_SCK: 23
    SPI_0_SLAVE_PIN_MOSI: 24
    SPI_0_SLAVE_PIN_MISO: 25
    SPI_0_SLAVE_PIN_SS: 22
    I2C_0_PIN_SCL: 27
    I2C_0_PIN_SDA: 26

    CONFIG_FCB_FLASH_AREA: FLASH_AREA_NFFS
    REBOOT_LOG_FLASH_AREA: FLASH_AREA_REBOOT_LOG
    NFFS_FLASH_AREA: FLASH_AREA_NFFS
    COREDUMP_FLASH_AREA: FLASH_AREA_IMAGE_1
    MCU_DCDC_ENABLED: 1
    MCU_LFCLK_SOURCE: LFXO
    BOOT_SERIAL_DETECT_PIN: 13  # Button 1

syscfg.vals.BLE_CONTROLLER:
    TIMER_0: 0
    TIMER_5: 1
    OS_CPUTIME_FREQ: 32768
    OS_CPUTIME_TIMER_NUM: 5
    BLE_LL_RFMGMT_ENABLE_TIME: 1500
    BLE_LL_STACK_SIZE: 4000
