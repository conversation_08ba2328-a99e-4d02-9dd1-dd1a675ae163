PTS test results for GAP

PTS version: 7.5.0
Tested: 27-Sept-2019

Results:
PASS	test passed
FAIL	test failed
INC	test is inconclusive
N/A	test is disabled due to PICS setup

-------------------------------------------------------------------------------
Test Name		Result	Notes
-------------------------------------------------------------------------------

GAP/BROB/BCST/BV-01-C	PASS	advertise-configure legacy=1 connectable=0 scannable=0
GAP/BROB/BCST/BV-02-C	PASS	advertise-configure legacy=1 connectable=0 scannable=0

GAP/BROB/BCST/BV-03-C	PASS	set irk=<IRK> e.g: 00:00:00:00:00:00:00:00:00:00:00:00:00:00:00:11
				Note: in PTS IXIT please set:
				TSPX_iut_device_IRK_for_resolvable_privacy_address_generation_procedure=11000000000000000000000000000000
				set advertise-set-adv-data name=<name> flags=4
				advertise-configure connectable=0 scannable=0 own_addr_type=rpa_pub
GAP/BROB/BCST/BV-04-C	PASS    TSPX_advertising_data=07086E696D626C65
				advertise-set-adv-data name=nimble
				set addr_type=random addr=01:3e:56:f7:46:21
				advertise-configure connectable=0 scannable=0 own_addr_type=random
GAP/BROB/BCST/BV-05-C	N/A
GAP/BROB/OBSV/BV-01-C	PASS	scan passive
GAP/BROB/OBSV/BV-02-C	PASS	scan
GAP/BROB/OBSV/BV-03-C	PASS	scan
GAP/BROB/OBSV/BV-04-C	PASS	connect peer_addr=<addr>
				security-set-data bonding=1
				security-pair conn=<handle>
				<OK>
				<OK>
GAP/BROB/OBSV/BV-05-C	PASS	scan own_addr_type=rpa_pub
GAP/BROB/OBSV/BV-06-C	PASS	scan own_addr_type=rpa_pub
-------------------------------------------------------------------------------

GAP/DISC/NONM/BV-01-C	PASS	advertise-configure connectable=0 legacy=1 adverdise=non
GAP/DISC/NONM/BV-02-C	PASS	advertise-configure connectable=0

GAP/DISC/LIMM/BV-01-C	N/A
GAP/DISC/LIMM/BV-02-C	N/A
GAP/DISC/LIMM/BV-03-C	PASS	advertise-configure legacy=1 connectable=0
				advertise-set-adv-data flags=5
				advertise-start duration= e.g.3000
GAP/DISC/LIMM/BV-04-C	PASS	advertise-configure legacy=1 connectable=0
				advertise-set-adv-data flags=5
				advertising-start duration=<e.g.3000>
GAP/DISC/GENM/BV-01-C	N/A
GAP/DISC/GENM/BV-02-C	N/A
GAP/DISC/GENM/BV-03-C	PASS	advertise-configure legacy=1 connectable=0
				advertise-set-adv-data flags=6
				advertise-start
GAP/DISC/GENM/BV-04-C	PASS	advertise-configure legacy=1 connectable=0
				advertise-set-adv-data flags=6
				advertising-start

GAP/DISC/LIMP/BV-01-C	PASS	scan limited=1 nodups=1
GAP/DISC/LIMP/BV-02-C	PASS	scan limited=1 nodups=1
GAP/DISC/LIMP/BV-03-C	PASS	scan limited=1 nodups=1
GAP/DISC/LIMP/BV-04-C	PASS	scan limited=1 nodups=1
GAP/DISC/LIMP/BV-05-C	PASS	scan limited=1 nodups=1

GAP/DISC/GENP/BV-01-C	PASS	scan nodups=1
GAP/DISC/GENP/BV-02-C	PASS	scan nodups=1
GAP/DISC/GENP/BV-03-C	PASS	scan nodups=1
				<OK>
GAP/DISC/GENP/BV-04-C	PASS	scan nodups=1
				<OK>
GAP/DISC/GENP/BV-05-C	PASS	scan nodups=1

GAP/DISC/RPA/BV-01-C	N/A		scan nodups=1
-------------------------------------------------------------------------------

GAP/IDLE/GIN/BV-01-C	N/A
GAP/IDLE/GIN/BV-02-C	N/A
GAP/IDLE/NAMP/BV-01-C	PASS	advertise-configure connectable=1 legacy=1
				advertising-start
				gatt-discover-full conn=<handle>
				gatt-show
				<check start end handle for 0x1800>
				gatt-read conn=<handle> uuid=0x2a00 start=<start> end=<end>
				disconnect conn=<handle>
GAP/IDLE/NAMP/BV-02-C	PASS	<answer NO to role question>
				advertise-configure connectable=1 legacy=1
				advertising-start
GAP/IDLE/DED/BV-01-C	N/A
GAP/IDLE/DED/BV-02-C	N/A
-------------------------------------------------------------------------------

GAP/CONN/NCON/BV-01-C	PASS	advertise-configure connectable=0 legacy=1
				advertising-start
GAP/CONN/NCON/BV-02-C	PASS	advertise-configure connectable=0 legacy=1
				advertise-set-adv-data flags=6
				advertise-start
GAP/CONN/NCON/BV-03-C	PASS	advertise-configure connectable=0 legacy=1
				advertise-set-adv-data flags=5
				advertise-start

GAP/CONN/DCON/BV-01-C	PASS	advertise-configure connectable=0 directed=1 peer_addr=<addr>
				advertise-start
GAP/CONN/DCON/BV-02-C	N/A
GAP/CONN/DCON/BV-03-C	N/A

GAP/CONN/UCON/BV-01-C	PASS	advertise-configure connectable=1 legacy=1
				advertise-set-adv-data flags=4
				advertise-start
GAP/CONN/UCON/BV-02_C	PASS	advertise-configure connectable=1 legacy=1
				advertise-set-adv-data flags=5
				advertise-start
GAP/CONN/UCON/BV-03_C	PASS	adbertise-configure connectable=1 legacy=1
				advertise-set-adv-data flags=6
GAP/CONN/UCON/BV-04_C	N/A
GAP/CONN/UCON/BV-05_C	N/A
GAP/CONN/UCON/BV-06_C	N/A

GAP/CONN/ACEP/BV-01-C	PASS	white-list addr_type=public addr=<addr>
				connect
				disconnect conn=<handle>
GAP/CONN/ACEP/BV-02-C	N/A

GAP/CONN/GCEP/BV-01-C	PASS	connect peer_addr=<addr>
				disconnect conn=<handle>
GAP/CONN/GCEP/BV-02-C	PASS	connect peer_addr=<addr>
GAP/CONN/GCEP/BV-03-C	PASS	set irk=<irk>
				connect peer_addr=<addr> own_addr_type=rpa_pub
				security-set-data bonding=1 our_key_dist=7 their_key_dist=7
				security-pair conn=<handle>
				connect peer_addr=<addr>
				disconnect conn=1
GAP/CONN/GCEP/BV-04-C	N/A
GAP/CONN/SCEP/BV-01-C	PASS	white-list addr_type=public addr=<addr>
				connect
				disconnect conn=<handle>
GAP/CONN/SCEP/BV-02-C	INC
GAP/CONN/DCEP/BV-01-C	PASS	connect peer_addr=<addr>
				disconnect conn=<handle>
GAP/CONN/DCEP/BV-02-C	INC
GAP/CONN/DCEP/BV-03-C	PASS	connect peer_addr=<addr>
				disconnect conn=<handle>
GAP/CONN/DCEP/BV-04-C	PASS	connect peer_addr=<addr>
				disconnect conn=<handle>

GAP/CONN/CPUP/BV-01-C	PASS	advertise-start
				conn-update-params conn=<handle>
GAP/CONN/CPUP/BV-02-C	PASS	advertise-start
				conn-update-params conn=<handle>
GAP/CONN/CPUP/BV-03-C	PASS	advertise-start
				conn-update-params conn=<handle>
GAP/CONN/CPUP/BV-04-C	PASS	connect peer_addr=<addr>
				disconnect conn=<handle>
GAP/CONN/CPUP/BV-05-C	PASS	connect peer_addr=<addr>
				disconnect conn=<handle>
GAP/CONN/CPUP/BV-06-C	PASS	conect peer_addr=<addr>
				conn-update-params conn=<handle> eg.latency=20
				disconnect conn=<handle>
GAP/CONN/CPUP/BV-08-C	PASS	advertise-configure legacy=1 connectable=1
				advertise-set-data name=<name>
				advertise-start

GAP/CONN/TERM/BV-01-C	PASS	connect peer_addr=<addr>
				disconnect conn=<handle>
GAP/CONN/PRDA/BV-01-C	N/A
GAP/CONN/PRDA/BV-02-C	N/A
-------------------------------------------------------------------------------
GAP/BOND/NBON/BV-01-C	PASS	security-set-data bonding=0
				connect peer_addr=<addr>
				<ok>
				connect peer_addr=<addr>
				<ok>
GAP/BOND/NBON/BV-02-C	PASS	security-set-data bonding=0
				connect peer_addr=<addr>
				security-pair conn=<handle>
				<ok>
				connect peer_addr=<addr>
				security-pair conn=<handle>
				<ok>
GAP/BOND/NBON/BV-03-C	PASS	security-set-data bonding=0
				advertise-configure legacy=1 connectable=1
				advertise-set-data name=<name>
				advertise-start
				<ok>

GAP/BOND/BON/BV-01-C	PASS	security-set-data bonding=1 sc=1 our_key_dist=7 their_key_dist=7
				advertise-configure legacy=1 connectable=1
				advertise-start
				security-start conn=<handle>
				<ok>
				advertise-start
				<ok>
GAP/BOND/BON/BV-02-C	PASS	security-set-data bonding=1
				connect peer_addr=<addr>
				security-pair conn=<handle>
				<ok>
				connect peer_addr=<addr>
				seccurity-pair conn=<handle>
				<ok>
GAP/BOND/BON/BV-03-C	PASS	security-set-sm-data bonding=1 our_key_dist=7 their_key_dist=7
				advertise-configure legacy=1 connectable=1
				advertise-start
				<ok>
				advertise-start
				<ok>
GAP/BOND/BON/BV-04-C	PASS	security-set-data bonding=1
				connect-peer_addr=<addr>
				disconnect conn=<handle>
				connect peer_addr=<addr>
				security-pair conn=<handle>
				disconnect conn=<handle>
-------------------------------------------------------------------------------

GAP/SEC/AUT/BV-11-C	PASS	security-set-data io_capabilities=1 sc=1
				advertise-configure legacy=1 connectable=1
				advertising-start
				Note: in PTS enter handle for characteristics
				      value which requires encryption for read (gatt-show-local)
				auth-passkey conn=<handle> action=3 key=123456
				Note: enter '123456' passkey in PTS
GAP/SEC/AUT/BV-12-C	PASS	security-set-data io_capabilities=1 bonding=1 mitm_flag=1 sc=1 our_key_dist=7 their_key_dist=7
				connect peer_addr=<addr>
				gatt-show-local
				Note: in PTS enter handle for characteristics
				      value which requires encryption for read
				auth-passkey conn=<handle> action=3 key=123456
				Note: enter '123456' passkey in PTS
GAP/SEC/AUT/BV-13-C	PASS	Note: in PTS confirm that IUT supports GATT Server
				security-set-data io_capabilities=1 bonding=1 mitm_flag=1 sc=1 our_key_dist=7 their_key_dist=7
				connect peer_addr=<addr>
				gatt-show-local
				Note: in PTS enter handle for characteristics
				      value which requires authenticated pairing for read
				auth-passkey conn=<handle> action=3 key=123456
				Note: enter '123456' passkey in PTS
GAP/SEC/AUT/BV-14-C	PASS	security-set-data io_capabilities=1
				advertise-configure legacy=1 connectable=1
				advertise-start
				gatt-show-local
				Note: in PTS enter handle for characteristics
				      value which requires authenticated pairing for read
				auth-passkey conn=<handle> action=3 key=123456
				Note: enter '123456' passkey in PTS
GAP/SEC/AUT/BV-15-C	N/A	security-set-data bonding=1 io_capabilities=4 mitm_flag=1 sc=1 our_key_dist=7 their_key_dist=7
				advertise-configure legacy=1 connectable=1
				advertise-start
				auth-passkey conn=<handle> action=2 key=<key>
				advertise-start
				gatt-show-local
				Note: in PTS enter handle for characteristics
				      value which requires authenticated pairing for read
GAP/SEC/AUT/BV-16-C	N/A	security-set-data io_capabilities=1 bonding=1 mitm_flag=1 sc=1 our_key_dist=7 their_key_dist=7
				connect peer_addr=<addr>
				auth-passkey conn=<handle> action=3 key=123456
				Note: enter '123456' passkey in PTS
				connect peer_addr=<addr>
				gatt-show-local
				Note: in PTS enter handle for characteristics
				      value which requires authenticated pairing for read
GAP/SEC/AUT/BV-17-C	N/A
GAP/SEC/AUT/BV-18-C	N/A
GAP/SEC/AUT/BV-19-C	N/A
GAP/SEC/AUT/BV-20-C	N/A
GAP/SEC/AUT/BV-21-C	N/A
GAP/SEC/AUT/BV-22-C	N/A
GAP/SEC/AUT/BV-23-C	N/A
GAP/SEC/AUT/BV-24-C	N/A

GAP/SEC/CSIGN/BV-01-C	N/A
GAP/SEC/CSIGN/BV-02-C	N/A

GAP/SEC/CSIGN/BI-01-C	N/A
GAP/SEC/CSIGN/BI-02-C	N/A
GAP/SEC/CSIGN/BI-03-C	N/A
GAP/SEC/CSIGN/BI-04-C	N/A
-------------------------------------------------------------------------------

GAP/PRIV/CONN/BV-01-C	N/A
GAP/PRIV/CONN/BV-02-C	N/A
GAP/PRIV/CONN/BV-03-C	N/A
GAP/PRIV/CONN/BV-04-C	INC
GAP/PRIV/CONN/BV-05-C	N/A
GAP/PRIV/CONN/BV-06-C	N/A
GAP/PRIV/CONN/BV-07-C	N/A
GAP/PRIV/CONN/BV-08-C	N/A
GAP/PRIV/CONN/BV-09-C	N/A
GAP/PRIV/CONN/BV-10-C	N/A
GAP/PRIV/CONN/BV-11-C	N/A
-------------------------------------------------------------------------------

GAP/ADV/BV-01-C		PASS	advertise-set-adv_data uuid16=0x1802
				advertise-start
				advertise-stop
GAP/ADV/BV-02-C		PASS	advertise-set-adv_data name=<name>
				advertise-start
				advertise-stop
GAP/ADV/BV-03-C		PASS	advertise-set-adv_data flags=6
				advertise-start
				advertise-stop
GAP/ADV/BV-04-C		PASS	advertise-set-adv_data mfg_data=ff:ff
				advertise-start
				advertise-stop
GAP/ADV/BV-05-C		PASS	advertise-set-adv_data tx_pwr_lvl=10
				advertise-start
				advertise-stop
GAP/ADV/BV-08-C		N/A
GAP/ADV/BV-09-C		N/A
GAP/ADV/BV-10-C		PASS	advetrise-set-adv_data service_data_uuid16=18:02:ff:ff
				advertise-start
				advertise-stop
GAP/ADV/BV-11-C		PASS	advertise-set -dv_data appearance=12
				advertise-start
				advertise-stop
GAP/ADV/BV-12-C		N/A
GAP/ADV/BV-13-C		N/A
GAP/ADV/BV-14-C		N/A
GAP/ADV/BV-15-C		N/A
GAP/ADV/BV-16-C		N/A
GAP/ADV/BV-17-C		PASS	In PTS: TSPX_URI=<bytes>
				set-adv-data uri=<bytes>
				advertise-start
				advertise-stop
-------------------------------------------------------------------------------

GAP/GAT/BV-01-C		PASS	<if NO>
				advertising-start
				<if YES>
				connect peer_addr=<addr>
GAP/GAT/BV-02-C		N/A
GAP/GAT/BV-03-C		N/A
GAP/GAT/BV-04-C		PASS	advertise-configure connectable=1 legacy=1
				advertise-start
GAP/GAT/BV-05-C		N/A
GAP/GAT/BV-06-C		N/A
GAP/GAT/BV-07-C		N/A
GAP/GAT/BV-08-C		N/A
----------------------------------------------------------------------------

GAP/DM/NCON/BV-01-C	N/A
GAP/DM/CON/BV-01-C	N/A
GAP/DM/NBON/BV-01-C	N/A
GAP/DM/BON/BV-01-C	N/A
GAP/DM/GIN/BV-01-C	N/A
GAP/DM/LIN/BV-01-C	N/A
GAP/DM/NAD/BV-01-C	N/A
GAP/DM/NAD/BV-02-C	N/A
GAP/DM/LEP/BV-01-C	N/A
GAP/DM/LEP/BV-02-C	N/A
GAP/DM/LEP/BV-04-C	N/A
GAP/DM/LEP/BV-05-C	N/A
GAP/DM/LEP/BV-06-C	N/A
GAP/DM/LEP/BV-07-C	N/A
GAP/DM/LEP/BV-08-C	N/A
GAP/DM/LEP/BV-09-C	N/A
GAP/DM/LEP/BV-10-C	N/A
GAP/DM/LEP/BV-11-C	N/A
-------------------------------------------------------------------------------

GAP/MOD/NDIS/BV-01-C	N/A
GAP/MOD/LDIS/BV-01-C	N/A
GAP/MOD/LDIS/BV-02-C	N/A
GAP/MOD/LDIS/BV-03-C	N/A
GAP/MOD/GDIS/BV-01-C	N/A
GAP/MOD/GDIS/BV-02-C	N/A
GAP/MOD/NCON/BV-01-C	N/A
GAP/MOD/CON/BV-01-C	N/A