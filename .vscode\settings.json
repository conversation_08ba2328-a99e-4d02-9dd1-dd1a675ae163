{"files.associations": {"compare": "c", "cstdint": "c", "nimble_opt_auto.h": "c", "ble_ll_sched.h": "c", "cstdlib": "c", "string.h": "c", "sysinit.h": "c", "stddef.h": "c", "ble_hci_emspi.h": "c", "cstdarg": "cpp", "stdlib.h": "c", "nimble_npl_log.h": "c", "prov.h": "c", "nimble_npl_os_log.h": "c", "ble_hs_test_util.h": "c", "ble_uuid.h": "c", "syscfg.h": "c", "random": "cpp", "os_mempool.h": "c", "ble_hs.h": "c", "ble_gatt.h": "c", "console.h": "c", "ble_svc_gap.h": "c", "ble_store_config.h": "c", "ble_store_config_priv.h": "c", "os.h": "c", "errno.h": "c", "ble.h": "c", "ble_gap.h": "c", "ble_hs_priv.h": "c", "hci_common.h": "c"}}