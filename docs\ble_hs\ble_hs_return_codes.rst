NimBLE Host Return Codes
------------------------

.. contents::
    :local:
    :depth: 2

Introduction
~~~~~~~~~~~~

Summary
^^^^^^^

The NimBLE host reports status to the application via a set of return codes. The host encompasses several layers of the Bluetooth specification that each defines its own set of status codes. Rather than "abstract away" information from lower layers that the application developer might find useful, the NimBLE host aims to indicate precisely what happened when something fails. Consequently, the host utilizes a rather large set of return codes.

A return code of 0 indicates success. For failure conditions, the return codes are partitioned into five separate sets:

+---------------------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Set                       | Condition                                                                                                                                                                                                   |
+===========================+=============================================================================================================================================================================================================+
| Core                      | Errors detected internally by the NimBLE host.                                                                                                                                                              |
+---------------------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| ATT                       | The ATT server has reported a failure via the transmission of an ATT Error Response. The return code corresponds to the value of the Error Code field in the response.                                      |
+---------------------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| HCI                       | The controller has reported an error to the host via a command complete or command status HCI event. The return code corresponds to the value of the Status field in the event.                             |
+---------------------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| L2CAP                     | An L2CAP signaling procedure has failed and an L2CAP Command Reject was sent as a result. The return code corresponds to the value of the Reason field in the command.                                      |
+---------------------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Security manager (us)     | The host detected an error during a security manager procedure and sent a Pairing Failed command to the peer. The return code corresponds to the value of the Reason field in the Pairing Failed command.   |
+---------------------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+
| Security manager (peer)   | A security manager procedure failed because the peer sent us a Pairing Failed command. The return code corresponds to the value of the Reason field in the Pairing Failed command.                          |
+---------------------------+-------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------+

The return codes in the core set are defined by the NimBLE Host. The other sets are defined in the Bluetooth specification; the codes in this latter group are referred to as *formal status codes*. As defined in the Bluetooth specification, the formal status code sets are not disjoint. That is, they overlap. For example, the spec defines a status code of 1 to have all of the following meanings:

+---------+----------------------------+
| Layer   | Meaning                    |
+=========+============================+
| ATT     | Invalid handle.            |
+---------+----------------------------+
| HCI     | Unknown HCI command.       |
+---------+----------------------------+
| L2CAP   | Signalling MTU exceeded.   |
+---------+----------------------------+
| SM      | Passkey entry failed.      |
+---------+----------------------------+

Clearly, the host can't just return an unadorned formal status code and expect the application to make sense of it. To resolve this ambiguity, the NimBLE host divides the full range of an int into several subranges. Each subrange corresponds to one of the five return code sets. For example, the ATT set is mapped onto the subrange *[0x100, 0x200)*. To indicate an ATT error of 3 (write not permitted), the NimBLE host returns a value 0x103 to the application.

The host defines a set of convenience macros for converting from a formal status code to NimBLE host status code. These macros are documented in the table below.

+----------------------------+---------------------------+--------------+
| Macro                      | Status code set           | Base value   |
+============================+===========================+==============+
| BLE\_HS\_ATT\_ERR()        | ATT                       | 0x100        |
+----------------------------+---------------------------+--------------+
| BLE\_HS\_HCI\_ERR()        | HCI                       | 0x200        |
+----------------------------+---------------------------+--------------+
| BLE\_HS\_L2C\_ERR()        | L2CAP                     | 0x300        |
+----------------------------+---------------------------+--------------+
| BLE\_HS\_SM\_US\_ERR()     | Security manager (us)     | 0x400        |
+----------------------------+---------------------------+--------------+
| BLE\_HS\_SM\_PEER\_ERR()   | Security manager (peer)   | 0x500        |
+----------------------------+---------------------------+--------------+

Example
^^^^^^^

The following example demonstrates how an application might determine which error is being reported by the host. In this example, the application performs the GAP encryption procedure and checks the return code. To simplify the example, the application uses a hypothetical *my\_blocking\_enc\_proc()* function, which blocks until the pairing operation has completed.

.. code:: c

    void
    encrypt_connection(uint16_t conn_handle)
    {
        int rc;

        /* Perform a blocking GAP encryption procedure. */
        rc = my_blocking_enc_proc(conn_handle);
        switch (rc) {
        case 0:
            console_printf("success - link successfully encrypted\n");
            break;

        case BLE_HS_ENOTCONN:
            console_printf("failure - no connection with handle %d\n",
                           conn_handle);
            break;

        case BLE_HS_ERR_SM_US_BASE(BLE_SM_ERR_CONFIRM_MISMATCH):
            console_printf("failure - mismatch in peer's confirm and random "
                           "commands.\n");
            break;

        case BLE_HS_ERR_SM_PEER_BASE(BLE_SM_ERR_CONFIRM_MISMATCH):
            console_printf("failure - peer reports mismatch in our confirm and "
                           "random commands.\n");
            break;

        default:
            console_printf("failure - other error: 0x%04x\n", rc);
            break;
        }
    }

Return Code Reference
~~~~~~~~~~~~~~~~~~~~~

Header
^^^^^^

All NimBLE host return codes are made accessible by including the following header:

.. code:: c

    #include "host/ble_hs.h"

Return codes - Core
^^^^^^^^^^^^^^^^^^^

The precise meaning of each of these error codes depends on the function that returns it.
The API reference for a particular function indicates the conditions under which each of these codes are returned.

+---------+------------------------------+---------------------------------------------------------------------------------------------+
| Value   | Name                         | Condition                                                                                   |
+=========+==============================+=============================================================================================+
| 0x00    | *N/A*                        | Success                                                                                     |
+---------+------------------------------+---------------------------------------------------------------------------------------------+
| 0x01    | BLE\_HS\_EAGAIN              | Temporary failure; try again.                                                               |
+---------+------------------------------+---------------------------------------------------------------------------------------------+
| 0x02    | BLE\_HS\_EALREADY            | Operation already in progress or completed.                                                 |
+---------+------------------------------+---------------------------------------------------------------------------------------------+
| 0x03    | BLE\_HS\_EINVAL              | One or more arguments are invalid.                                                          |
+---------+------------------------------+---------------------------------------------------------------------------------------------+
| 0x04    | BLE\_HS\_EMSGSIZE            | The provided buffer is too small.                                                           |
+---------+------------------------------+---------------------------------------------------------------------------------------------+
| 0x05    | BLE\_HS\_ENOENT              | No entry matching the specified criteria.                                                   |
+---------+------------------------------+---------------------------------------------------------------------------------------------+
| 0x06    | BLE\_HS\_ENOMEM              | Operation failed due to resource exhaustion.                                                |
+---------+------------------------------+---------------------------------------------------------------------------------------------+
| 0x07    | BLE\_HS\_ENOTCONN            | No open connection with the specified handle.                                               |
+---------+------------------------------+---------------------------------------------------------------------------------------------+
| 0x08    | BLE\_HS\_ENOTSUP             | Operation disabled at compile time.                                                         |
+---------+------------------------------+---------------------------------------------------------------------------------------------+
| 0x09    | BLE\_HS\_EAPP                | Application callback behaved unexpectedly.                                                  |
+---------+------------------------------+---------------------------------------------------------------------------------------------+
| 0x0a    | BLE\_HS\_EBADDATA            | Command from peer is invalid.                                                               |
+---------+------------------------------+---------------------------------------------------------------------------------------------+
| 0x0b    | BLE\_HS\_EOS                 | Mynewt OS error.                                                                            |
+---------+------------------------------+---------------------------------------------------------------------------------------------+
| 0x0c    | BLE\_HS\_ECONTROLLER         | Event from controller is invalid.                                                           |
+---------+------------------------------+---------------------------------------------------------------------------------------------+
| 0x0d    | BLE\_HS\_ETIMEOUT            | Operation timed out.                                                                        |
+---------+------------------------------+---------------------------------------------------------------------------------------------+
| 0x0e    | BLE\_HS\_EDONE               | Operation completed successfully.                                                           |
+---------+------------------------------+---------------------------------------------------------------------------------------------+
| 0x0f    | BLE\_HS\_EBUSY               | Operation cannot be performed until procedure completes.                                    |
+---------+------------------------------+---------------------------------------------------------------------------------------------+
| 0x10    | BLE\_HS\_EREJECT             | Peer rejected a connection parameter update request.                                        |
+---------+------------------------------+---------------------------------------------------------------------------------------------+
| 0x11    | BLE\_HS\_EUNKNOWN            | Unexpected failure; catch all.                                                              |
+---------+------------------------------+---------------------------------------------------------------------------------------------+
| 0x12    | BLE\_HS\_EROLE               | Operation requires different role (e.g., central vs. peripheral).                           |
+---------+------------------------------+---------------------------------------------------------------------------------------------+
| 0x13    | BLE\_HS\_ETIMEOUT\_HCI       | HCI request timed out; controller unresponsive.                                             |
+---------+------------------------------+---------------------------------------------------------------------------------------------+
| 0x14    | BLE\_HS\_ENOMEM\_EVT         | Controller failed to send event due to memory exhaustion (combined host-controller only).   |
+---------+------------------------------+---------------------------------------------------------------------------------------------+
| 0x15    | BLE\_HS\_ENOADDR             | Operation requires an identity address but none configured.                                 |
+---------+------------------------------+---------------------------------------------------------------------------------------------+
| 0x16    | BLE\_HS\_ENOTSYNCED          | Attempt to use the host before it is synced with controller.                                |
+---------+------------------------------+---------------------------------------------------------------------------------------------+
| 0x17    | BLE\_HS\_EAUTHEN             | Insufficient authentication.                                                                |
+---------+------------------------------+---------------------------------------------------------------------------------------------+
| 0x18    | BLE\_HS\_EAUTHOR             | Insufficient authorization.                                                                 |
+---------+------------------------------+---------------------------------------------------------------------------------------------+
| 0x19    | BLE\_HS\_EENCRYPT            | Insufficient encryption level.                                                              |
+---------+------------------------------+---------------------------------------------------------------------------------------------+
| 0x1a    | BLE\_HS\_EENCRYPT\_KEY\_SZ   | Insufficient key size.                                                                      |
+---------+------------------------------+---------------------------------------------------------------------------------------------+
| 0x1b    | BLE\_HS\_ESTORE\_CAP         | Storage at capacity.                                                                        |
+---------+------------------------------+---------------------------------------------------------------------------------------------+
| 0x1c    | BLE\_HS\_ESTORE\_FAIL        | Storage IO error.                                                                           |
+---------+------------------------------+---------------------------------------------------------------------------------------------+

Return codes - ATT
^^^^^^^^^^^^^^^^^^

+----------------+----------------+--------------------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| NimBLE Value   | Formal Value   | Name                                       | Condition                                                                                                                                 |
+================+================+============================================+===========================================================================================================================================+
| 0x0101         | 0x01           | BLE\_ATT\_ERR\_INVALID\_HANDLE             | The attribute handle given was not valid on this server.                                                                                  |
+----------------+----------------+--------------------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x0102         | 0x02           | BLE\_ATT\_ERR\_READ\_NOT\_PERMITTED        | The attribute cannot be read.                                                                                                             |
+----------------+----------------+--------------------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x0103         | 0x03           | BLE\_ATT\_ERR\_WRITE\_NOT\_PERMITTED       | The attribute cannot be written.                                                                                                          |
+----------------+----------------+--------------------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x0104         | 0x04           | BLE\_ATT\_ERR\_INVALID\_PDU                | The attribute PDU was invalid.                                                                                                            |
+----------------+----------------+--------------------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x0105         | 0x05           | BLE\_ATT\_ERR\_INSUFFICIENT\_AUTHEN        | The attribute requires authentication before it can be read or written.                                                                   |
+----------------+----------------+--------------------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x0106         | 0x06           | BLE\_ATT\_ERR\_REQ\_NOT\_SUPPORTED         | Attribute server does not support the request received from the client.                                                                   |
+----------------+----------------+--------------------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x0107         | 0x07           | BLE\_ATT\_ERR\_INVALID\_OFFSET             | Offset specified was past the end of the attribute.                                                                                       |
+----------------+----------------+--------------------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x0108         | 0x08           | BLE\_ATT\_ERR\_INSUFFICIENT\_AUTHOR        | The attribute requires authorization before it can be read or written.                                                                    |
+----------------+----------------+--------------------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x0109         | 0x09           | BLE\_ATT\_ERR\_PREPARE\_QUEUE\_FULL        | Too many prepare writes have been queued.                                                                                                 |
+----------------+----------------+--------------------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x010a         | 0x0a           | BLE\_ATT\_ERR\_ATTR\_NOT\_FOUND            | No attribute found within the given attribute handle range.                                                                               |
+----------------+----------------+--------------------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x010b         | 0x0b           | BLE\_ATT\_ERR\_ATTR\_NOT\_LONG             | The attribute cannot be read or written using the Read Blob Request.                                                                      |
+----------------+----------------+--------------------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x010c         | 0x0c           | BLE\_ATT\_ERR\_INSUFFICIENT\_KEY\_SZ       | The Encryption Key Size used for encrypting this link is insufficient.                                                                    |
+----------------+----------------+--------------------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x010d         | 0x0d           | BLE\_ATT\_ERR\_INVALID\_ATTR\_VALUE\_LEN   | The attribute value length is invalid for the operation.                                                                                  |
+----------------+----------------+--------------------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x010e         | 0x0e           | BLE\_ATT\_ERR\_UNLIKELY                    | The attribute request that was requested has encountered an error that was unlikely, and therefore could not be completed as requested.   |
+----------------+----------------+--------------------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x010f         | 0x0f           | BLE\_ATT\_ERR\_INSUFFICIENT\_ENC           | The attribute requires encryption before it can be read or written.                                                                       |
+----------------+----------------+--------------------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x0110         | 0x10           | BLE\_ATT\_ERR\_UNSUPPORTED\_GROUP          | The attribute type is not a supported grouping attribute as defined by a higher layer specification.                                      |
+----------------+----------------+--------------------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x0111         | 0x11           | BLE\_ATT\_ERR\_INSUFFICIENT\_RES           | Insufficient Resources to complete the request.                                                                                           |
+----------------+----------------+--------------------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+

Return codes - HCI
^^^^^^^^^^^^^^^^^^

+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| NimBLE Value   | Formal Value   | Name                               | Condition                                                                      |
+================+================+====================================+================================================================================+
| 0x0201         | 0x01           | BLE\_ERR\_UNKNOWN\_HCI\_CMD        | Unknown HCI Command                                                            |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0202         | 0x02           | BLE\_ERR\_UNK\_CONN\_ID            | Unknown Connection Identifier                                                  |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0203         | 0x03           | BLE\_ERR\_HW\_FAIL                 | Hardware Failure                                                               |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0204         | 0x04           | BLE\_ERR\_PAGE\_TMO                | Page Timeout                                                                   |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0205         | 0x05           | BLE\_ERR\_AUTH\_FAIL               | Authentication Failure                                                         |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0206         | 0x06           | BLE\_ERR\_PINKEY\_MISSING          | PIN or Key Missing                                                             |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0207         | 0x07           | BLE\_ERR\_MEM\_CAPACITY            | Memory Capacity Exceeded                                                       |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0208         | 0x08           | BLE\_ERR\_CONN\_SPVN\_TMO          | Connection Timeout                                                             |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0209         | 0x09           | BLE\_ERR\_CONN\_LIMIT              | Connection Limit Exceeded                                                      |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x020a         | 0x0a           | BLE\_ERR\_SYNCH\_CONN\_LIMIT       | Synchronous Connection Limit To A Device Exceeded                              |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x020b         | 0x0b           | BLE\_ERR\_ACL\_CONN\_EXISTS        | ACL Connection Already Exists                                                  |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x020c         | 0x0c           | BLE\_ERR\_CMD\_DISALLOWED          | Command Disallowed                                                             |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x020d         | 0x0d           | BLE\_ERR\_CONN\_REJ\_RESOURCES     | Connection Rejected due to Limited Resources                                   |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x020e         | 0x0e           | BLE\_ERR\_CONN\_REJ\_SECURITY      | Connection Rejected Due To Security Reasons                                    |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x020f         | 0x0f           | BLE\_ERR\_CONN\_REJ\_BD\_ADDR      | Connection Rejected due to Unacceptable BD\_ADDR                               |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0210         | 0x10           | BLE\_ERR\_CONN\_ACCEPT\_TMO        | Connection Accept Timeout Exceeded                                             |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0211         | 0x11           | BLE\_ERR\_UNSUPPORTED              | Unsupported Feature or Parameter Value                                         |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0212         | 0x12           | BLE\_ERR\_INV\_HCI\_CMD\_PARMS     | Invalid HCI Command Parameters                                                 |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0213         | 0x13           | BLE\_ERR\_REM\_USER\_CONN\_TERM    | Remote User Terminated Connection                                              |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0214         | 0x14           | BLE\_ERR\_RD\_CONN\_TERM\_RESRCS   | Remote Device Terminated Connection due to Low Resources                       |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0215         | 0x15           | BLE\_ERR\_RD\_CONN\_TERM\_PWROFF   | Remote Device Terminated Connection due to Power Off                           |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0216         | 0x16           | BLE\_ERR\_CONN\_TERM\_LOCAL        | Connection Terminated By Local Host                                            |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0217         | 0x17           | BLE\_ERR\_REPEATED\_ATTEMPTS       | Repeated Attempts                                                              |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0218         | 0x18           | BLE\_ERR\_NO\_PAIRING              | Pairing Not Allowed                                                            |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0219         | 0x19           | BLE\_ERR\_UNK\_LMP                 | Unknown LMP PDU                                                                |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x021a         | 0x1a           | BLE\_ERR\_UNSUPP\_REM\_FEATURE     | Unsupported Remote Feature / Unsupported LMP Feature                           |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x021b         | 0x1b           | BLE\_ERR\_SCO\_OFFSET              | SCO Offset Rejected                                                            |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x021c         | 0x1c           | BLE\_ERR\_SCO\_ITVL                | SCO Interval Rejected                                                          |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x021d         | 0x1d           | BLE\_ERR\_SCO\_AIR\_MODE           | SCO Air Mode Rejected                                                          |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x021e         | 0x1e           | BLE\_ERR\_INV\_LMP\_LL\_PARM       | Invalid LMP Parameters / Invalid LL Parameters                                 |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x021f         | 0x1f           | BLE\_ERR\_UNSPECIFIED              | Unspecified Error                                                              |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0220         | 0x20           | BLE\_ERR\_UNSUPP\_LMP\_LL\_PARM    | Unsupported LMP Parameter Value / Unsupported LL Parameter Value               |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0221         | 0x21           | BLE\_ERR\_NO\_ROLE\_CHANGE         | Role Change Not Allowed                                                        |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0222         | 0x22           | BLE\_ERR\_LMP\_LL\_RSP\_TMO        | LMP Response Timeout / LL Response Timeout                                     |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0223         | 0x23           | BLE\_ERR\_LMP\_COLLISION           | LMP Error Transaction Collision                                                |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0224         | 0x24           | BLE\_ERR\_LMP\_PDU                 | LMP PDU Not Allowed                                                            |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0225         | 0x25           | BLE\_ERR\_ENCRYPTION\_MODE         | Encryption Mode Not Acceptable                                                 |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0226         | 0x26           | BLE\_ERR\_LINK\_KEY\_CHANGE        | Link Key cannot be Changed                                                     |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0227         | 0x27           | BLE\_ERR\_UNSUPP\_QOS              | Requested QoS Not Supported                                                    |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0228         | 0x28           | BLE\_ERR\_INSTANT\_PASSED          | Instant Passed                                                                 |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0229         | 0x29           | BLE\_ERR\_UNIT\_KEY\_PAIRING       | Pairing With Unit Key Not Supported                                            |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x022a         | 0x2a           | BLE\_ERR\_DIFF\_TRANS\_COLL        | Different Transaction Collision                                                |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x022c         | 0x2c           | BLE\_ERR\_QOS\_PARM                | QoS Unacceptable Parameter                                                     |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x022d         | 0x2d           | BLE\_ERR\_QOS\_REJECTED            | QoS Rejected                                                                   |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x022e         | 0x2e           | BLE\_ERR\_CHAN\_CLASS              | Channel Classification Not Supported                                           |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x022f         | 0x2f           | BLE\_ERR\_INSUFFICIENT\_SEC        | Insufficient Security                                                          |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0230         | 0x30           | BLE\_ERR\_PARM\_OUT\_OF\_RANGE     | Parameter Out Of Mandatory Range                                               |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0232         | 0x32           | BLE\_ERR\_PENDING\_ROLE\_SW        | Role Switch Pending                                                            |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0234         | 0x34           | BLE\_ERR\_RESERVED\_SLOT           | Reserved Slot Violation                                                        |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0235         | 0x35           | BLE\_ERR\_ROLE\_SW\_FAIL           | Role Switch Failed                                                             |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0236         | 0x36           | BLE\_ERR\_INQ\_RSP\_TOO\_BIG       | Extended Inquiry Response Too Large                                            |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0237         | 0x37           | BLE\_ERR\_SEC\_SIMPLE\_PAIR        | Secure Simple Pairing Not Supported By Host                                    |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0238         | 0x38           | BLE\_ERR\_HOST\_BUSY\_PAIR         | Host Busy - Pairing                                                            |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0239         | 0x39           | BLE\_ERR\_CONN\_REJ\_CHANNEL       | Connection Rejected due to No Suitable Channel Found                           |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x023a         | 0x3a           | BLE\_ERR\_CTLR\_BUSY               | Controller Busy                                                                |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x023b         | 0x3b           | BLE\_ERR\_CONN\_PARMS              | Unacceptable Connection Parameters                                             |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x023c         | 0x3c           | BLE\_ERR\_DIR\_ADV\_TMO            | Directed Advertising Timeout                                                   |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x023d         | 0x3d           | BLE\_ERR\_CONN\_TERM\_MIC          | Connection Terminated due to MIC Failure                                       |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x023e         | 0x3e           | BLE\_ERR\_CONN\_ESTABLISHMENT      | Connection Failed to be Established                                            |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x023f         | 0x3f           | BLE\_ERR\_MAC\_CONN\_FAIL          | MAC Connection Failed                                                          |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+
| 0x0240         | 0x40           | BLE\_ERR\_COARSE\_CLK\_ADJ         | Coarse Clock Adjustment Rejected but Will Try to Adjust Using Clock Dragging   |
+----------------+----------------+------------------------------------+--------------------------------------------------------------------------------+

Return codes - L2CAP
^^^^^^^^^^^^^^^^^^^^

+----------------+----------------+----------------------------------------------+------------------------------------------------------+
| NimBLE Value   | Formal Value   | Name                                         | Condition                                            |
+================+================+==============================================+======================================================+
| 0x0300         | 0x00           | BLE\_L2CAP\_SIG\_ERR\_CMD\_NOT\_UNDERSTOOD   | Invalid or unsupported incoming L2CAP sig command.   |
+----------------+----------------+----------------------------------------------+------------------------------------------------------+
| 0x0301         | 0x01           | BLE\_L2CAP\_SIG\_ERR\_MTU\_EXCEEDED          | Incoming packet too large.                           |
+----------------+----------------+----------------------------------------------+------------------------------------------------------+
| 0x0302         | 0x02           | BLE\_L2CAP\_SIG\_ERR\_INVALID\_CID           | No channel with specified ID.                        |
+----------------+----------------+----------------------------------------------+------------------------------------------------------+

Return codes - Security manager (us)
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

+----------------+----------------+-----------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| NimBLE Value   | Formal Value   | Name                              | Condition                                                                                                                                 |
+================+================+===================================+===========================================================================================================================================+
| 0x0401         | 0x01           | BLE\_SM\_ERR\_PASSKEY             | The user input of passkey failed, for example, the user cancelled the operation.                                                          |
+----------------+----------------+-----------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x0402         | 0x02           | BLE\_SM\_ERR\_OOB                 | The OOB data is not available.                                                                                                            |
+----------------+----------------+-----------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x0403         | 0x03           | BLE\_SM\_ERR\_AUTHREQ             | The pairing procedure cannot be performed as authentication requirements cannot be met due to IO capabilities of one or both devices.     |
+----------------+----------------+-----------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x0404         | 0x04           | BLE\_SM\_ERR\_CONFIRM\_MISMATCH   | The confirm value does not match the calculated compare value.                                                                            |
+----------------+----------------+-----------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x0405         | 0x05           | BLE\_SM\_ERR\_PAIR\_NOT\_SUPP     | Pairing is not supported by the device.                                                                                                   |
+----------------+----------------+-----------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x0406         | 0x06           | BLE\_SM\_ERR\_ENC\_KEY\_SZ        | The resultant encryption key size is insufficient for the security requirements of this device.                                           |
+----------------+----------------+-----------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x0407         | 0x07           | BLE\_SM\_ERR\_CMD\_NOT\_SUPP      | The SMP command received is not supported on this device.                                                                                 |
+----------------+----------------+-----------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x0408         | 0x08           | BLE\_SM\_ERR\_UNSPECIFIED         | Pairing failed due to an unspecified reason.                                                                                              |
+----------------+----------------+-----------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x0409         | 0x09           | BLE\_SM\_ERR\_REPEATED            | Pairing or authentication procedure is disallowed because too little time has elapsed since last pairing request or security request.     |
+----------------+----------------+-----------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x040a         | 0x0a           | BLE\_SM\_ERR\_INVAL               | The Invalid Parameters error code indicates that the command length is invalid or that a parameter is outside of the specified range.     |
+----------------+----------------+-----------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x040b         | 0x0b           | BLE\_SM\_ERR\_DHKEY               | Indicates to the remote device that the DHKey Check value received doesn’t match the one calculated by the local device.                  |
+----------------+----------------+-----------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x040c         | 0x0c           | BLE\_SM\_ERR\_NUMCMP              | Indicates that the confirm values in the numeric comparison protocol do not match.                                                        |
+----------------+----------------+-----------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x040d         | 0x0d           | BLE\_SM\_ERR\_ALREADY             | Indicates that the pairing over the LE transport failed due to a Pairing Request sent over the BR/EDR transport in process.               |
+----------------+----------------+-----------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x040e         | 0x0e           | BLE\_SM\_ERR\_CROSS\_TRANS        | Indicates that the BR/EDR Link Key generated on the BR/EDR transport cannot be used to derive and distribute keys for the LE transport.   |
+----------------+----------------+-----------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+

Return codes - Security manager (peer)
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

+----------------+----------------+-----------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| NimBLE Value   | Formal Value   | Name                              | Condition                                                                                                                                 |
+================+================+===================================+===========================================================================================================================================+
| 0x0501         | 0x01           | BLE\_SM\_ERR\_PASSKEY             | The user input of passkey failed, for example, the user cancelled the operation.                                                          |
+----------------+----------------+-----------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x0502         | 0x02           | BLE\_SM\_ERR\_OOB                 | The OOB data is not available.                                                                                                            |
+----------------+----------------+-----------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x0503         | 0x03           | BLE\_SM\_ERR\_AUTHREQ             | The pairing procedure cannot be performed as authentication requirements cannot be met due to IO capabilities of one or both devices.     |
+----------------+----------------+-----------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x0504         | 0x04           | BLE\_SM\_ERR\_CONFIRM\_MISMATCH   | The confirm value does not match the calculated compare value.                                                                            |
+----------------+----------------+-----------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x0505         | 0x05           | BLE\_SM\_ERR\_PAIR\_NOT\_SUPP     | Pairing is not supported by the device.                                                                                                   |
+----------------+----------------+-----------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x0506         | 0x06           | BLE\_SM\_ERR\_ENC\_KEY\_SZ        | The resultant encryption key size is insufficient for the security requirements of this device.                                           |
+----------------+----------------+-----------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x0507         | 0x07           | BLE\_SM\_ERR\_CMD\_NOT\_SUPP      | The SMP command received is not supported on this device.                                                                                 |
+----------------+----------------+-----------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x0508         | 0x08           | BLE\_SM\_ERR\_UNSPECIFIED         | Pairing failed due to an unspecified reason.                                                                                              |
+----------------+----------------+-----------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x0509         | 0x09           | BLE\_SM\_ERR\_REPEATED            | Pairing or authentication procedure is disallowed because too little time has elapsed since last pairing request or security request.     |
+----------------+----------------+-----------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x050a         | 0x0a           | BLE\_SM\_ERR\_INVAL               | The Invalid Parameters error code indicates that the command length is invalid or that a parameter is outside of the specified range.     |
+----------------+----------------+-----------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x050b         | 0x0b           | BLE\_SM\_ERR\_DHKEY               | Indicates to the remote device that the DHKey Check value received doesn’t match the one calculated by the local device.                  |
+----------------+----------------+-----------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x050c         | 0x0c           | BLE\_SM\_ERR\_NUMCMP              | Indicates that the confirm values in the numeric comparison protocol do not match.                                                        |
+----------------+----------------+-----------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x050d         | 0x0d           | BLE\_SM\_ERR\_ALREADY             | Indicates that the pairing over the LE transport failed due to a Pairing Request sent over the BR/EDR transport in process.               |
+----------------+----------------+-----------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
| 0x050e         | 0x0e           | BLE\_SM\_ERR\_CROSS\_TRANS        | Indicates that the BR/EDR Link Key generated on the BR/EDR transport cannot be used to derive and distribute keys for the LE transport.   |
+----------------+----------------+-----------------------------------+-------------------------------------------------------------------------------------------------------------------------------------------+
