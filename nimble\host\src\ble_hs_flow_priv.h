/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing,
 * software distributed under the License is distributed on an
 * "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
 * KIND, either express or implied.  See the License for the
 * specific language governing permissions and limitations
 * under the License.
 */

#ifndef H_BLE_HS_FLOW_PRIV_
#define H_BLE_HS_FLOW_PRIV_

#include <inttypes.h>
#ifdef __cplusplus
extern "C" {
#endif

void ble_hs_flow_connection_broken(uint16_t conn_handle);
void ble_hs_flow_track_data_mbuf(struct os_mbuf *om);
int ble_hs_flow_startup(void);

#ifdef __cplusplus
}
#endif

#endif
