# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

syscfg.defs:
    BLE_TRANSPORT: 1

    BLE_TRANSPORT_HS:
        description: >
            Select HCI transport used towards host.
            "native" means NimBLE host is running on the same core and no
            transport is required.
        value: native
        choices:
            - native
            - dialog_cmac
            - nrf5340
            - uart
            - usb
            - cdc
            - custom
    BLE_TRANSPORT_LL:
        description: >
            Select HCI transport used towards controller.
            "native" means NimBLE controller is running on the same core and no
            transport is required.
        value: native
        choices:
            - native
            - emspi
            - dialog_cmac
            - nrf5340
            - socket
            - apollo3
            - uart_ll
            - custom

    BLE_TRANSPORT_ACL_COUNT:
        description: >
            Number of ACL buffers available in transport. This determines
            number of buffers used by host/controller for flow control.
            Buffers pool is allocated for each non-native transport side
            selected, unless overriden by BLE_TRANSPORT_ACL_FROM_[HS|LL]_COUNT.
        value: 10
    BLE_TRANSPORT_ACL_SIZE:
        description: Size of each buffer in ACL pool.
        value: 251
    BLE_TRANSPORT_ISO_COUNT:
        description: >
            Number of ISO buffers available in transport. This determines
            number of buffers used by host/controller for flow control.
            Buffers pool is allocated for each non-native transport side
            selected, unless overriden by BLE_TRANSPORT_ISO_FROM_[HS|LL]_COUNT.
        value: 10
    BLE_TRANSPORT_ISO_SIZE:
        description: Size of each buffer in ISO pool.
        value: 300
    BLE_TRANSPORT_EVT_COUNT:
        description: >
            Number of event buffers available in transport.
        value: 4
    BLE_TRANSPORT_EVT_DISCARDABLE_COUNT:
        description: >
            Number of discardable event buffers available in transport.
            Discardable event buffers are used for events that can be safely
            dropped by controller if there's no free buffer available, e.g.
            advertising reports.
        value: 16
    BLE_TRANSPORT_EVT_SIZE:
        description: >
            Size of each buffer in events pool. This applies to both regular
            and discardable buffers. Value is automatically adjusted if
            extended advertising is enabled so in most cases it's better to
            leave it at default settings.
        value: 70

    BLE_TRANSPORT_ACL_FROM_HS_COUNT:
        description: >
            Overrides BLE_TRANSPORT_ACL_COUNT on host side, i.e. number
            of ACL buffers available for host.
        value: MYNEWT_VAL(BLE_TRANSPORT_ACL_COUNT)
    BLE_TRANSPORT_ACL_FROM_LL_COUNT:
        description: >
            Overrides BLE_TRANSPORT_ACL_COUNT on controller side, i.e. number
            of ACL buffers available for controller.
        value: MYNEWT_VAL(BLE_TRANSPORT_ACL_COUNT)

    BLE_TRANSPORT_ISO_FROM_HS_COUNT:
        description: >
            Overrides BLE_TRANSPORT_ISO_COUNT on host side, i.e. number
            of ISO buffers available for host.
        value: MYNEWT_VAL(BLE_TRANSPORT_ISO_COUNT)
    BLE_TRANSPORT_ISO_FROM_LL_COUNT:
        description: >
            Overrides BLE_TRANSPORT_ISO_COUNT on controller side, i.e. number
            of ISO buffers available for controller.
        value: MYNEWT_VAL(BLE_TRANSPORT_ISO_COUNT)

# import monitor and defunct settings from separate file to reduce clutter in main file
$import:
    - "@apache-mynewt-nimble/nimble/transport/syscfg.monitor.yml"
    - "@apache-mynewt-nimble/nimble/transport/syscfg.defunct.yml"

syscfg.vals.'BLE_EXT_ADV || BLE_LL_CFG_FEAT_LL_EXT_ADV':
    BLE_TRANSPORT_EVT_SIZE: 257
