# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

syscfg.defs:
    BTSHELL_ANS:
        description: Include support for the alert notification service.
        value: 1

syscfg.vals:
    CONSOLE_IMPLEMENTATION: full
    LOG_IMPLEMENTATION: full
    STATS_IMPLEMENTATION: full

    # Enable the shell task.
    SHELL_TASK: 1

    # Set log level to info (disable debug logging).
    LOG_LEVEL: 1

    # Disable security manager (pairing and bonding).
    BLE_SM_LEGACY: 0
    BLE_SM_SC: 0

    # Default task settings
    OS_MAIN_STACK_SIZE: 512

    # SMP is not supported in this app, so disable smp-over-shell.
    SHELL_MGMT: 0

    # Whether to save data to sys/config, or just keep it in RAM.
    BLE_STORE_CONFIG_PERSIST: 0

    # L2CAP COC SDU buffers in RX endpoint
    BLE_L2CAP_COC_SDU_BUFF_COUNT: 1

syscfg.vals.BLE_MESH:
    MSYS_1_BLOCK_COUNT: 16
