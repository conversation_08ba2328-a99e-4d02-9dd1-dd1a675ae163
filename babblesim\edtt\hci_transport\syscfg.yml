#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

syscfg.defs:
    EDTT_HCI_LOG_FILE:
        description: >
            Path to the HCI log file. Skip file extension
            because device id and .log will be appended.
        value: "hci_logs"
    EDTT_HCI_LOGS:
        description: Turn on HCI commands logging.
        value: 0
    EDTT_POLLER_PRIO:
        description: 'Priority of native EDTT poller task.'
        type: task_priority
        value: 2

syscfg.vals:
    BLE_TRANSPORT_ACL_COUNT: 32
    BLE_TRANSPORT_EVT_COUNT: 64

syscfg.restrictions:
    - BLE_TRANSPORT_HS == "custom"
    - BLE_TRANSPORT_LL == "native"