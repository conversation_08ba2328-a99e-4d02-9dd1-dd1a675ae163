# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

syscfg.defs:
    BLE_SOCK_USE_TCP:
        description: 'Use TCP socket, connects to BLE_SOCK_TCP_PORT'
        value: 1

    BLE_SOCK_TCP_PORT:
        description: 'ipv4 tcp port to connect to'
        value: 14433

    BLE_SOCK_USE_LINUX_BLUE:
        description: 'Use Linux bluetooth raw socket'
        value: 0

    BLE_SOCK_LINUX_DEV:
        description: 'linux kernel device'
        value: 0

    BLE_SOCK_USE_NUTTX:
        description: 'Use NuttX socket'
        value: 0

    BLE_SOCK_TASK_PRIO:
        description: 'Priority of the HCI socket task.'
        type: task_priority
        value: 9

    BLE_SOCK_STACK_SIZE:
        description: 'Size of the HCI socket stack (units=words).'
        value: 80

    BLE_SOCK_CLI_SYSINIT_STAGE:
        description: >
            Sysinit stage for the socket BLE transport.
        value: 500
