#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

name: Compliance check

on: [pull_request]

jobs:
  style_check:
    name: Coding style
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - name: Install Dependencies
        shell: bash
        run: |
             sudo apt-get update
             sudo apt-get install -y uncrustify
             mkdir repos
             git clone --depth=1 https://github.com/apache/mynewt-core repos/apache-mynewt-core
      - name: check style
        shell: bash
        run: |
             ./repos/apache-mynewt-core/.github/check_style.py

  style_license:
    name: Licensing
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - name: Install Dependencies
        shell: bash
        run: |
             sudo apt-get update
             sudo apt-get install -y libxml2-utils
             mkdir repos
             git clone --depth=1 https://github.com/apache/mynewt-core repos/apache-mynewt-core
             wget https://repository.apache.org/content/repositories/snapshots/org/apache/rat/apache-rat/maven-metadata.xml -O snapshot.xml
             SNAPSHOT=`xmllint --xpath "//latest/text()" snapshot.xml`
             wget https://repository.apache.org/content/repositories/snapshots/org/apache/rat/apache-rat/$SNAPSHOT/maven-metadata.xml -O version.xml
             VERSION=`xmllint --xpath "//snapshotVersion[1]/value/text()" version.xml`
             wget https://repository.apache.org/content/repositories/snapshots/org/apache/rat/apache-rat/$SNAPSHOT/apache-rat-$VERSION.jar -O apache-rat.jar
      - name: check licensing
        shell: bash
        run: |
             ./repos/apache-mynewt-core/.github/check_license.py
