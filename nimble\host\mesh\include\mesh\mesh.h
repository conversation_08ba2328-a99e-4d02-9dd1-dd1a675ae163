/** @file
 *  @brief Bluetooth Mesh Profile APIs.
 */

/*
 * Copyright (c) 2017 Intel Corporation
 *
 * SPDX-License-Identifier: Apache-2.0
 */
#ifndef __BT_MESH_H
#define __BT_MESH_H

#include <stddef.h>
#include "syscfg/syscfg.h"
#include "os/os_mbuf.h"

#include "glue.h"
#include "msg.h"
#include "access.h"
#include "main.h"
#include "cfg.h"
#include "cfg_srv.h"
#include "health_srv.h"
#include "cfg_cli.h"
#include "health_cli.h"
#include "proxy.h"
#include "cdb.h"
#include "cfg.h"
#include "heartbeat.h"
#include "../src/app_keys.h"
#include "../src/net.h"

#endif /* __BT_MESH_H */
