# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

syscfg.defs:
    BLE_MESH_PROV:
        description: >
            Enable provisioning. It is automatically enabled whenever
            BLE_MESH_PB_ADV or BLE_MESH_PB_GATT is set.
        value: 1

    BLE_MESH_PROV_OOB_PUBLIC_KEY:
        description: >
            Enable this option if public key is to be exchanged via Out of Band (OOB) technology.
        value: 0

    BLE_MESH_PB_ADV:
        description: >
           Enable this option to allow the device to be provisioned over
           the advertising bearer.
        value: 1

    BLE_MESH_UNPROV_BEACON_INT:
        description: >
           This option specifies the interval (in seconds) at which the
           device sends unprovisioned beacon.
        value: 5

    BLE_MESH_PB_ADV_RETRANS_TIMEOUT:
        description: >
           Timeout value of retransmit provisioning PDUs.
        value: 500

    BLE_MESH_PROV_DEVICE:
        description: >
           Enable this option to allow the device to be provisioned into a mesh network.
        value: 1
        restrictions: BLE_MESH_PROV

    BLE_MESH_PROVISIONER:
        description: >
            Enable this option to have support for provisioning remote devices.
        value: 1
        restrictions:
            - BLE_MESH_PROV
            - BLE_MESH_PB_ADV

    BLE_MESH_CDB:
        description: >
            Mesh Configuration Database [EXPERIMENTAL]
        value: 0

    BLE_MESH_CDB_NODE_COUNT:
        description: >
            This option specifies how many nodes each network can at most
            save in the configuration database.
        value: 8

    BLE_MESH_CDB_SUBNET_COUNT:
        description: >
            This option specifies how many subnets that can at most be
            saved in the configuration database.
        value: 1

    BLE_MESH_CDB_APP_KEY_COUNT:
        description: >
            This option specifies how many application keys that can at most
            be saved in the configuration database.
        value: 1

    BLE_MESH_DEBUG_CDB:
        description:
            Use this option to enable configuration database debug logs.
        value: 1

    BLE_MESH_DEBUG_CFG:
        description:
            Use this option to enable node configuration debug logs for the
            Bluetooth Mesh functionality.
        value: 1

    BLE_MESH_PROXY:
        description: >
           Enable proxy. This is automatically set whenever BLE_MESH_PB_GATT or
           BLE_MESH_GATT_PROXY is set.
        value: 0

    BLE_MESH_GATT:
        value: 1

    BLE_MESH_PROXY_MSG_LEN:
        description: >
            Integer value
        value:
        restrictions: BLE_MESH_GATT

    BLE_MESH_GATT_SERVER:
        value: 1
        restrictions: BLE_MESH_GATT

    BLE_MESH_PB_GATT:
        description: >
           Enable this option to allow the device to be provisioned over
           the GATT bearer.
        value: 1
        restrictions:
            - '(BLE_MESH_GATT_SERVER && BLE_MESH_PROV)'

    BLE_MESH_PB_GATT_USE_DEVICE_NAME:
        description: >
            This option includes GAP device name in scan response when
            the PB-GATT is enabled.
        value: 1

    BLE_MESH_GATT_PROXY:
        description: >
            This option enables support for the Mesh GATT Proxy Service,
            i.e. the ability to act as a proxy between a Mesh GATT Client
            and a Mesh network.
        value: 1
        restrictions:
            - '(BLE_MESH_GATT_SERVER && BLE_MESH_PROXY)'

    BLE_MESH_GATT_PROXY_ENABLED:
        description: >
            Controls whether the GATT Proxy feature is enabled by default.
            Can be changed through runtime configuration.
        value: 1
        restrictions: BLE_MESH_GATT_PROXY

    BLE_MESH_DEFAULT_TTL:
        description: >
            Controls the default TTL value for outgoing messages. Can be changed
            through runtime configuration.
        value: 7

    BLE_MESH_NODE_ID_TIMEOUT:
        description: >
            This option determines for how long the local node advertises
            using Node Identity. The given value is in seconds. The
            specification limits this to 60 seconds, and implies that to
            be the appropriate value as well, so just leaving this as the
            default is the safest option.
        value: 60

    BLE_MESH_PROXY_FILTER_SIZE:
        descryption: >
            This option specifies how many Proxy Filter entries the local
            node supports.
        value: 3
        restrictions: BLE_MESH_GATT_PROXY

    BLE_MESH_ACCESS_LAYER_MSG:
        descryption: >
            This option allows the applicaiton to directly access
            Bluetooth access layer messages without the need to
            instantiate Bluetooth mesh models.
        value: 1

    BLE_MESH_PROXY_USE_DEVICE_NAME:
        description: >
            Include Bluetooth device name in scan response
        value: 0
        restrictions: BLE_MESH_GATT_PROXY

    BLE_MESH_SUBNET_COUNT:
        description: >
            This option specifies how many subnets a Mesh network can
            participate in at the same time.
        value: 1

    BLE_MESH_APP_KEY_COUNT:
        description: >
            This option specifies how many application keys the device can
            store per network.
        value: 1

    BLE_MESH_MODEL_KEY_COUNT:
        description: >
             This option specifies how many application keys each model can
             at most be bound to.
        value: 1

    BLE_MESH_MODEL_GROUP_COUNT:
        description: >
            This option specifies how many group addresses each model can
            at most be subscribed to.
        value: 1

    BLE_MESH_MODEL_VND_MSG_CID_FORCE:
        description: >
            This option forces vendor model to use messages for the
              corresponding CID field.
        value: 1

    BLE_MESH_LABEL_COUNT:
        description: >
            This option specifies how many Label UUIDs can be stored.
        value: 1

    BLE_MESH_CRPL:
        description: >
            This options specifies the maximum capacity of the replay
            protection list. This option is similar to the network message
            cache size, but has a different purpose.
        value: 10

    BLE_MESH_ADV_TASK_PRIO:
        description: >
            Advertising task prio (FIXME)
        type: task_priority
        value: 9

    BLE_MESH_MSG_CACHE_SIZE:
        description: >
            Number of messages that are cached for the network. This description
            prevent unnecessary decryption operations and unnecessary
            relays. This option is similar to the replay protection list,
            but has a different purpose.
        value: 10

    BLE_MESH_NET_BUF_USER_DATA_SIZE:
        description: >
            Number of octets that are used as user_data at the end of os_mbufs
        value: 4

    BLE_MESH_ADV_BUF_COUNT:
        description: >
            Number of advertising buffers available. This should be chosen
            based on what kind of features the local node shoule have. E.g.
            a relay will perform better the more buffers it has. Another
            thing to consider is outgoing segmented messages. There must
            be at least three more advertising buffers than the maximum
            supported outgoing segment count (BT_MESH_TX_SEG_MAX).
        value: 6

    BLE_MESH_ADV:
        description: >
            Advertiser mode
        value: 1

    BLE_MESH_ADV_LEGACY:
        description: >
            Use legacy advertising commands for mesh sending. Legacy
            advertising is significantly slower than the extended advertising, but
            is supported by all controllers.
        value: 1
        restrictions:
            - "BLE_MESH_ADV if 0"
            - "!BLE_MESH_ADV_EXT"

    BLE_MESH_ADV_EXT:
        description: >
            Use extended advertising commands for operating the advertiser.
            Extended advertising is faster and uses less memory than legacy
            advertising, but isn't supported by all controllers.
        value: 0
        restrictions:
            - "BLE_MESH_ADV if 0"
            - "!BLE_MESH_ADV_LEGACY"
            - "BLE_EXT_ADV"

    BLE_MESH_DEBUG_USE_ID_ADDR:
        description: >
            Use ID address for mesh advertisements, use random address otherwise.
        value: 0

    BLE_MESH_ADV_STACK_SIZE:
        description: >
            Mesh advertiser thread stack size.
            NOTE: This is an advanced setting and should not be changed unless
            absolutely necessary
        value: 768

    BLE_MESH_IV_UPDATE_SEQ_LIMIT:
        description: >
            This option specifies the sequence number value to start iv update.
        value: 0x800000

    BLE_MESH_IVU_DIVIDER:
        description: >
            When the IV Update state enters Normal operation or IV Update
            in Progress, we need to keep track of how many hours has passed
            in the state, since the specification requires us to remain in
            the state at least for 96 hours (Update in Progress has an
            additional upper limit of 144 hours).

            In order to fulfil the above requirement, even if the node might
            be powered off once in a while, we need to store persistently
            how many hours the node has been in the state. This doesn't
            necessarily need to happen every hour (thanks to the flexible
            duration range). The exact cadence will depend a lot on the
            ways that the node will be used and what kind of power source it
            has.

            Since there is no single optimal answer, this configuration
            option allows specifying a divider, i.e. how many intervals
            the 96 hour minimum gets split into. After each interval the
            duration that the node has been in the current state gets
            stored to flash. E.g. the default value of 4 means that the
            state is saved every 24 hours (96 / 4).
        value: 4

    BLE_MESH_TX_SEG_MSG_COUNT:
        description: >
            Maximum number of simultaneous outgoing multi-segment and/or
            reliable messages.
        value: 1

    BLE_MESH_RX_SEG_MSG_COUNT:
        description: >
            Maximum number of simultaneous incoming multi-segment and/or
            reliable messages.
        value: 2

    BLE_MESH_SEG_BUFS:
        description: >
            The incoming and outgoing segmented messages allocate their
            segments from the same pool. Each segment is a 12 byte block,
            and may only be used by one message at the time.

            Outgoing messages will allocate their segments at the start of the
            transmission, and release them one by one as soon as they have been
            acknowledged by the receiver. Incoming messages allocate all their
            segments at the start of the transaction, and won't release them until
            the message is fully received.
        value:
            64

    BLE_MESH_RX_SEG_MAX:
        description: >
            Maximum number of segments supported for incoming messages.
            This value should typically be fine-tuned based on what
            models the local node supports, i.e. what's the largest
            message payload that the node needs to be able to receive.
            This value affects memory and call stack consumption, which
            is why the default is lower than the maximum that the
            specification would allow (32 segments).

            The maximum incoming SDU size is 12 times this number (out of
            which 4 or 8 bytes is used for the Transport Layer MIC). For
            example, 5 segments means the maximum SDU size is 60 bytes,
            which leaves 56 bytes for application layer data using a
            4-byte MIC and 52 bytes using an 8-byte MIC.
        value:
            3



    BLE_MESH_TX_SEG_MAX:
        description: >
            Maximum number of segments supported for outgoing messages.
            This value should typically be fine-tuned based on what
            models the local node supports, i.e. what's the largest
            message payload that the node needs to be able to send.
            This value affects memory consumption, which is why the
            default is lower than the maximum that the specification
            would allow (32 segments).

            The maximum outgoing SDU size is 12 times this number (out of
            which 4 or 8 bytes is used for the Transport Layer MIC). For
            example, 5 segments means the maximum SDU size is 60 bytes,
            which leaves 56 bytes for application layer data using a
            4-byte MIC and 52 bytes using an 8-byte MIC.
        value: 3

    BLE_MESH_LOOPBACK_BUFS:
        description: >
            The number of buffers allocated for the network loopback mechanism.
            Loopback is used when the device sends messages to itself.
        value: 3

    BLE_MESH_TX_SEG_RETRANS_COUNT:
        description: >
            Maximum number of transport message segment retransmit attempts
            for outgoing segment message.
        value: 4

    BLE_MESH_TX_SEG_RETRANS_TIMEOUT_UNICAST:
        description: >
             Maximum time of retransmit segment message to unicast address.
        value: 400

    BLE_MESH_TX_SEG_RETRANS_TIMEOUT_GROUP:
        description: >
              Maximum time of retransmit segment message to group address.
        value: 50

    BLE_MESH_SEG_RETRANSMIT_ATTEMPTS:
        description: >
            Number of retransmit attempts (after the initial transmit) per segment
        value: 4
        retrictions: 'BLE_MESH_SEG_RETRANSMIT_ATTEMPTS > 1'

    BLE_MESH_NETWORK_TRANSMIT_COUNT:
        description: >
            Controls the initial number of retransmissions of original messages,
            in addition to the first transmission. Can be changed through runtime
            configuration.
        value: 2

    BLE_MESH_NETWORK_TRANSMIT_INTERVAL:
        description: >
            Controls the initial interval between retransmissions of original
            messages, in milliseconds. Can be changed through runtime
            configuration.
        value: 20

    BLE_MESH_RELAY:
        description: >
            Support for acting as a Mesh Relay Node.
        value: 1

    BLE_MESH_RELAY_ENABLED:
        description: >
            Controls whether the Mesh Relay feature is enabled by default. Can be
            changed through runtime configuration.
        value: MYNEWT_VAL(BLE_MESH_RELAY)

    BLE_MESH_RELAY_RETRANSMIT_COUNT:
        description: >
            Controls the initial number of retransmissions of relayed messages, in
            addition to the first transmission. Can be changed through runtime
            configuration.
        value: 2

    BLE_MESH_RELAY_RETRANSMIT_INTERVAL:
        description: >
            Controls the initial interval between retransmissions of relayed
            messages, in milliseconds. Can be changed through runtime
            configuration.
        value: 20

    BLE_MESH_BEACON_ENABLED:
        description: >
            Controls whether the Secure network beacon feature is enabled by
            default. Can be changed through runtime configuration.
        value: 1

    BLE_MESH_LOW_POWER:
        description: >
           Enable this option to be able to act as a Low Power Node.
        value: 0

    BLE_MESH_LPN_ESTABLISHMENT:
        description: >
           Perform the Friendship establishment using low power, with
           the help of a reduced scan duty cycle. The downside of this
           is that the node may miss out on messages intended for it
           until it has successfully set up Friendship with a Friend
           node.
        value: 1

    BLE_MESH_LPN_AUTO:
        description: >
           Automatically enable LPN functionality once provisioned and start
           looking for Friend nodes. If this option is disabled LPN mode
           needs to be manually enabled by calling bt_mesh_lpn_set(true).
           node.
        value: 1

    BLE_MESH_LPN_AUTO_TIMEOUT:
        description: >
           Time in seconds from the last received message, that the node
           will wait before starting to look for Friend nodes.
        value: 15

    BLE_MESH_LPN_RETRY_TIMEOUT:
        description: >
           Time in seconds between Friend Requests, if a previous Friend
           Request did not receive any acceptable Friend Offers.
        value: 8

    BLE_MESH_LPN_RSSI_FACTOR:
        description: >
            The contribution of the RSSI measured by the Friend node used
            in Friend Offer Delay calculations. 0 = 1, 1 = 1.5, 2 = 2, 3 = 2.5.
        value: 0

    BLE_MESH_LPN_RECV_WIN_FACTOR:
        description: >
           The contribution of the supported Receive Window used in
           Friend Offer Delay calculations. 0 = 1, 1 = 1.5, 2 = 2, 3 = 2.5.
        value: 0

    BLE_MESH_LPN_MIN_QUEUE_SIZE:
        description: >
           The MinQueueSizeLog field is defined as log_2(N), where N is
           the minimum number of maximum size Lower Transport PDUs that
           the Friend node can store in its Friend Queue. As an example,
           MinQueueSizeLog value 1 gives N = 2, and value 7 gives N = 128.
        value: 1

    BLE_MESH_LPN_RECV_DELAY:
        description: >
            The ReceiveDelay is the time between the Low Power node
            sending a request and listening for a response. This delay
            allows the Friend node time to prepare the response. The value
            is in units of milliseconds.
        value: 100

    BLE_MESH_LPN_POLL_TIMEOUT:
        description: >
            PollTimeout timer is used to measure time between two
            consecutive requests sent by the Low Power node. If no
            requests are received by the Friend node before the
            PollTimeout timer expires, then the friendship is considered
            terminated. The value is in units of 100 milliseconds, so e.g.
            a value of 300 means 30 seconds.
        value: 300

    BLE_MESH_LPN_INIT_POLL_TIMEOUT:
        description: >
            The initial value of the PollTimeout timer when Friendship
            gets established for the first time. After this the timeout
            will gradually grow toward the actual PollTimeout, doubling
            in value for each iteration. The value is in units of 100
            milliseconds, so e.g. a value of 300 means 3 seconds.
        value: MYNEWT_VAL_BLE_MESH_LPN_POLL_TIMEOUT

    BLE_MESH_LPN_SCAN_LATENCY:
        description: >
            Latency in milliseconds that it takes to enable scanning. This
            is in practice how much time in advance before the Receive Window
            that scanning is requested to be enabled.
        value: 10

    BLE_MESH_LPN_GROUPS:
        description: >
            Maximum number of groups that the LPN can subscribe to.
        value: 10

    BLE_MESH_LPN_SUB_ALL_NODES_ADDR:
        description: >
            Automatically subscribe all nodes address when friendship
            established.
        value: 0

    BLE_MESH_FRIEND:
        description: >
            Enable this option to be able to act as a Friend Node.
        value: 0

    BLE_MESH_FRIEND_ENABLED:
        description: >
            Controls whether the Friend feature is enabled by default. Can be
            changed through runtime configuration.
        value: 1

    BLE_MESH_FRIEND_RECV_WIN:
        description: >
            Receive Window in milliseconds supported by the Friend node.
        value: 255

    BLE_MESH_FRIEND_QUEUE_SIZE:
        description: >
            Minimum number of buffers available to be stored for each
            local Friend Queue.
        value: 16

    BLE_MESH_FRIEND_SUB_LIST_SIZE:
        description: >
            Size of the Subscription List that can be supported by a
            Friend node for a Low Power node.
        value: 3

    BLE_MESH_FRIEND_LPN_COUNT:
        description: >
            Number of Low Power Nodes the Friend can have a Friendship
            with simultaneously.
        value: 2


    BLE_MESH_FRIEND_SEG_RX:
        description: >
            Number of incomplete segment lists that we track for each LPN
            that we are Friends for. In other words, this determines how
            many elements we can simultaneously be receiving segmented
            messages from when the messages are going into the Friend queue.
        value: 1


    BLE_MESH_CFG_CLI:
        description: >
            Enable support for the configuration client model.
        value: 0

    BLE_MESH_HEALTH_CLI:
        description: >
            Enable support for the health client model.
        value: 0

    BLE_MESH_SHELL:
        description: >
            Activate shell module that provides Bluetooth Mesh commands to
            the console.
        value: 0

    BLE_MESH_MODEL_EXTENSIONS:
        description: >
            Enable support for the model extension concept, allowing the Access
            layer to know about Mesh model relationships.
        value: 0

    BLE_MESH_IV_UPDATE_TEST:
        description: >
            This option removes the 96 hour limit of the IV Update
            Procedure and lets the state be changed at any time.
        value: 0

    BLE_MESH_TESTING:
        description: >
            This option enables testing API.
        value: 0

    BLE_MESH_DEV_UUID:
        description: >
            Device UUID
        value: ((uint8_t[16]){0x11, 0x22, 0})

    BLE_MESH_SHELL_MODELS:
        description: >
            Include implementation of some demo models.
        value: 0

    BLE_MESH_OOB_OUTPUT_ACTIONS:
        description: >
            Supported Output OOB Actions
            BT_MESH_NO_OUTPUT       = 0,
            BT_MESH_BLINK           = BIT(0)
            BT_MESH_BEEP            = BIT(1)
            BT_MESH_VIBRATE         = BIT(2)
            BT_MESH_DISPLAY_NUMBER  = BIT(3)
            BT_MESH_DISPLAY_STRING  = BIT(4)
        value: ((BT_MESH_DISPLAY_NUMBER))

    BLE_MESH_OOB_OUTPUT_SIZE:
        description: >
            Output OOB size
        value: 4

    BLE_MESH_OOB_INPUT_ACTIONS:
        description: >
            Supported Input OOB Actions
            BT_MESH_NO_INPUT      = 0,
            BT_MESH_PUSH          = BIT(0)
            BT_MESH_TWIST         = BIT(1)
            BT_MESH_ENTER_NUMBER  = BIT(2)
            BT_MESH_ENTER_STRING  = BIT(3)
        value: ((BT_MESH_NO_INPUT))

    BLE_MESH_OOB_INPUT_SIZE:
        description: >
            Input OOB size
        value: 4

    BLE_MESH_SETTINGS:
        description: >
            This option enables Mesh settings storage.
        value: 1

    BLE_MESH_STORE_TIMEOUT:
        description: >
            This value defines in seconds how soon any pending changes
            are actually written into persistent storage (flash) after
            a change occurs.
        value: 2

    BLE_MESH_SEQ_STORE_RATE:
        description: >
            This value defines how often the local sequence number gets
            updated in persistent storage (i.e. flash). E.g. a value of 100
            means that the sequence number will be stored to flash on every
            100th increment. If the node sends messages very frequently a
            higher value makes more sense, whereas if the node sends
            infrequently a value as low as 0 (update storage for every
            increment) can make sense. When the stack gets initialized it
            will add this number to the last stored one, so that it starts
            off with a value that's guaranteed to be larger than the last
            one used before power off.
        value: 128

    BLE_MESH_RPL_STORE_TIMEOUT:
        description: >
            Minimum interval after which unsaved RPL entries are updated in storage

            This value defines in seconds how soon unsaved RPL entries
            gets written to the persistent storage. Setting this value
            to a large number may lead to security vulnerabilities if a node
            gets powered off before the timer is fired. When flash is used
            as the persistent storage setting this value to a low number
            may wear out flash sooner or later. However, if the RPL gets
            updated infrequently a value as low as 0 (write immediately)
            may make sense. Setting this value to -1 will disable this timer.
            In this case, a user is responsible to store pending RPL entries
            using @ref bt_mesh_rpl_pending_store. In the mean time, when
            IV Index is updated, the outdated RPL entries will still be
            stored by @ref BT_MESH_STORE_TIMEOUT. Finding the right balance
            between this timeout and calling @ref bt_mesh_rpl_pending_store
            may reduce a risk of security vulnerability and flash wear out.
        value: 5

    BLE_MESH_DEVICE_NAME:
        description: >
            This value defines BLE Mesh device/node name.
        value: '"nimble-mesh-node"'

    BLE_MESH_SYSINIT_STAGE:
        description: >
            Primary sysinit stage for BLE mesh functionality.
        value: 500

    BLE_MESH_SYSINIT_STAGE_SHELL:
        description: >
            Secondary sysinit stage for BLE mesh functionality.
        value: 1000

    ### Log settings.

    BLE_MESH_LOG_MOD:
        description: >
            Numeric module ID to use for BLE Mesh log messages.
        value: 9
    BLE_MESH_LOG_LVL:
        description: >
            Minimum level for the BLE Mesh log.
        value: 1

    BLE_MESH_ACCESS_LOG_MOD:
        description: >
            Numeric module ID to use for BLE Mesh Access-related log messages.
        value: 10
    BLE_MESH_ACCESS_LOG_LVL:
        description: >
            Minimum level for the BLE Mesh Access-related log.
        value: 1

    BLE_MESH_ADV_LOG_MOD:
        description: >
            Numeric module ID to use for BLE Mesh advertising log messages.
        value: 11
    BLE_MESH_ADV_LOG_LVL:
        description: >
            Minimum level for the BLE Mesh log.
        value: 1

    BLE_MESH_BEACON_LOG_MOD:
        description: >
            Numeric module ID to use for BLE Mesh Beacon-related log messages.
        value: 12
    BLE_MESH_BEACON_LOG_LVL:
        description: >
            Minimum level for the BLE Mesh Beacon-related log.
        value: 1

    BLE_MESH_CRYPTO_LOG_MOD:
        description: >
            Numeric module ID to use for BLE Mesh cryptographic log messages.
        value: 13
    BLE_MESH_CRYPTO_LOG_LVL:
        description: >
            Minimum level for the BLE Mesh cryptographic log.
        value: 1

    BLE_MESH_FRIEND_LOG_MOD:
        description: >
            Numeric module ID to use for BLE Mesh Friend log messages.
        value: 14
    BLE_MESH_FRIEND_LOG_LVL:
        description: >
            Minimum level for the BLE Mesh Friend log.
        value: 1

    BLE_MESH_LOW_POWER_LOG_MOD:
        description: >
            Numeric module ID to use for BLE Mesh Low Power log messages.
        value: 15
    BLE_MESH_LOW_POWER_LOG_LVL:
        description: >
            Minimum level for the BLE Mesh Low Power log.
        value: 1

    BLE_MESH_MODEL_LOG_MOD:
        description: >
            Numeric module ID to use for BLE Mesh Foundation Models log messages.
        value: 16
    BLE_MESH_MODEL_LOG_LVL:
        description: >
            Minimum level for the BLE Mesh Foundation Models log.
        value: 1

    BLE_MESH_NET_LOG_MOD:
        description: >
            Numeric module ID to use for BLE Mesh Network layer log messages.
        value: 17
    BLE_MESH_NET_LOG_LVL:
        description: >
            Minimum level for the BLE Mesh Network layer log.
        value: 1

    BLE_MESH_PROV_LOG_MOD:
        description: >
            Numeric module ID to use for BLE Mesh Provisioning log messages.
        value: 18
    BLE_MESH_PROV_LOG_LVL:
        description: >
            Minimum level for the BLE Mesh Provisioning log.
        value: 1

    BLE_MESH_PROXY_LOG_MOD:
        description: >
            Numeric module ID to use for BLE Mesh Proxy protocol log messages.
        value: 19
    BLE_MESH_PROXY_LOG_LVL:
        description: >
            Minimum level for the BLE Mesh Proxy protocol log.
        value: 1

    BLE_MESH_SETTINGS_LOG_MOD:
        description: >
            Numeric module ID to use for BLE Mesh persistent settings log messages.
        value: 20
    BLE_MESH_SETTINGS_LOG_LVL:
        description: >
            Minimum level for the BLE Mesh persistent settings log.
        value: 1

    BLE_MESH_TRANS_LOG_MOD:
        description: >
            Numeric module ID to use for BLE Mesh Transport Layer log messages.
        value: 21
    BLE_MESH_TRANS_LOG_LVL:
        description: >
            Minimum level for the BLE Mesh Transport Layer log.
        value: 1

    BLE_MESH_RPL_LOG_MOD:
        description: >
            Numeric module ID to use for BLE Mesh Replay protection list messages.
        value: 22
    BLE_MESH_RPL_LOG_LVL:
        description: >
            Minimum level for the BLE Mesh Replay protection list log.
        value: 1

    BLE_MESH_NET_KEYS_LOG_MOD:
        description: >
            Numeric module ID to use for BLE Mesh Replay protection list messages.
        value: 23
    BLE_MESH_NET_KEYS_LOG_LVL:
        description: >
            Minimum level for the BLE Mesh Replay protection list log.
        value: 1

    BLE_MESH_PROV_DEVICE_LOG_MOD:
        description: >
            Numeric module ID to use for BLE Mesh Replay protection list messages.
        value: 24
    BLE_MESH_PROV_DEVICE_LOG_LVL:
        description: >
            Minimum level for the BLE Mesh Replay protection list log.
        value: 1

    BLE_MESH_PROVISIONER_LOG_MOD:
        description: >
            Numeric module ID to use for BLE Mesh Replay protection list messages.
        value: 25
    BLE_MESH_PROVISIONER_LOG_LVL:
        description: >
            Minimum level for the BLE Mesh Replay protection list log.
        value: 1

    BLE_MESH_HEARTBEAT_LOG_MOD:
        description: >
            Numeric module ID to use for BLE Mesh Replay protection list messages.
        value: 26
    BLE_MESH_HEARTBEAT_LOG_LVL:
        description: >
            Minimum level for the BLE Mesh Replay protection list log.
        value: 1

syscfg.logs:
    BLE_MESH_LOG:
        module: MYNEWT_VAL(BLE_MESH_LOG_MOD)
        level: MYNEWT_VAL(BLE_MESH_LOG_LVL)

    BLE_MESH_ACCESS_LOG:
        module: MYNEWT_VAL(BLE_MESH_ACCESS_LOG_MOD)
        level: MYNEWT_VAL(BLE_MESH_ACCESS_LOG_LVL)

    BLE_MESH_ADV_LOG:
        module: MYNEWT_VAL(BLE_MESH_ADV_LOG_MOD)
        level: MYNEWT_VAL(BLE_MESH_ADV_LOG_LVL)

    BLE_MESH_BEACON_LOG:
        module: MYNEWT_VAL(BLE_MESH_BEACON_LOG_MOD)
        level: MYNEWT_VAL(BLE_MESH_BEACON_LOG_LVL)

    BLE_MESH_CRYPTO_LOG:
        module: MYNEWT_VAL(BLE_MESH_CRYPTO_LOG_MOD)
        level: MYNEWT_VAL(BLE_MESH_CRYPTO_LOG_LVL)

    BLE_MESH_FRIEND_LOG:
        module: MYNEWT_VAL(BLE_MESH_FRIEND_LOG_MOD)
        level: MYNEWT_VAL(BLE_MESH_FRIEND_LOG_LVL)

    BLE_MESH_LOW_POWER_LOG:
        module: MYNEWT_VAL(BLE_MESH_LOW_POWER_LOG_MOD)
        level: MYNEWT_VAL(BLE_MESH_LOW_POWER_LOG_LVL)

    BLE_MESH_MODEL_LOG:
        module: MYNEWT_VAL(BLE_MESH_MODEL_LOG_MOD)
        level: MYNEWT_VAL(BLE_MESH_MODEL_LOG_LVL)

    BLE_MESH_NET_LOG:
        module: MYNEWT_VAL(BLE_MESH_NET_LOG_MOD)
        level: MYNEWT_VAL(BLE_MESH_NET_LOG_LVL)

    BLE_MESH_PROV_LOG:
        module: MYNEWT_VAL(BLE_MESH_PROV_LOG_MOD)
        level: MYNEWT_VAL(BLE_MESH_PROV_LOG_LVL)

    BLE_MESH_PROXY_LOG:
        module: MYNEWT_VAL(BLE_MESH_PROXY_LOG_MOD)
        level: MYNEWT_VAL(BLE_MESH_PROXY_LOG_LVL)

    BLE_MESH_SETTINGS_LOG:
        module: MYNEWT_VAL(BLE_MESH_SETTINGS_LOG_MOD)
        level: MYNEWT_VAL(BLE_MESH_SETTINGS_LOG_LVL)

    BLE_MESH_RPL_LOG:
        module: MYNEWT_VAL(BLE_MESH_RPL_LOG_MOD)
        level: MYNEWT_VAL(BLE_MESH_RPL_LOG_LVL)

    BLE_MESH_TRANS_LOG:
        module: MYNEWT_VAL(BLE_MESH_TRANS_LOG_MOD)
        level: MYNEWT_VAL(BLE_MESH_TRANS_LOG_LVL)

    BLE_MESH_NET_KEYS_LOG:
        module: MYNEWT_VAL(BLE_MESH_NET_KEYS_LOG_MOD)
        level: MYNEWT_VAL(BLE_MESH_NET_KEYS_LOG_LVL)

    BLE_MESH_PROV_DEVICE_LOG:
        module: MYNEWT_VAL(BLE_MESH_PROV_DEVICE_LOG_MOD)
        level: MYNEWT_VAL(BLE_MESH_PROV_DEVICE_LOG_LVL)

    BLE_MESH_PROVISIONER_LOG:
        module: MYNEWT_VAL(BLE_MESH_PROVISIONER_LOG_MOD)
        level: MYNEWT_VAL(BLE_MESH_PROVISIONER_LOG_LVL)

    BLE_MESH_HEARTBEAT_LOG:
        module: MYNEWT_VAL(BLE_MESH_HEARTBEAT_LOG_MOD)
        level: MYNEWT_VAL(BLE_MESH_HEARTBEAT_LOG_LVL)

syscfg.vals.BLE_MESH_SHELL:
    BLE_MESH_CFG_CLI: 1
    BLE_MESH_HEALTH_CLI: 1
    BLE_MESH_IV_UPDATE_TEST: 1

syscfg.vals.BLE_MESH_GATT_PROXY:
    BLE_MESH_PROXY: 1

syscfg.vals.BLE_MESH_PB_GATT:
    BLE_MESH_PROXY: 1
    BLE_MESH_PROV: 1

syscfg.vals.BLE_MESH_PB_ADV:
    BLE_MESH_PROV: 1

syscfg.vals.'BLE_MESH_PB_GATT':
    BLE_MESH_PROXY_MSG_LEN: 66
syscfg.vals.'BLE_MESH_GATT_PROXY':
    BLE_MESH_PROXY_MSG_LEN: 33
