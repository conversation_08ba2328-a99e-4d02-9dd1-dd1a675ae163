#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

name: Build check

on: [push, pull_request]

jobs:
  ports:
    name: Build ports
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: carlosperate/arm-none-eabi-gcc-action@48db4484a55750df7a0ccca63347fcdea6534d78
        with:
          release: '12.2.Rel1'
      - name: Install Dependencies
        shell: bash
        run: |
             sudo apt-get update
             sudo apt-get install -y make ccache gcc-multilib g++-multilib
      - name: Build example ports
        shell: bash
        run: |
             make -C porting/examples/dummy/ clean all
             make -C porting/examples/linux/ clean all
             make -C porting/examples/linux_blemesh/ clean all
             make -C porting/npl/linux/test/ clean all test
      - name: Build RIOT port
        shell: bash
        if: success() || failure()
        continue-on-error: true
        run: |
             git clone --depth=1 https://github.com/RIOT-OS/RIOT
             rm RIOT/pkg/nimble/patches/ -rf
             sed -i 's|PKG_URL.*|PKG_URL = '$(pwd)'|' RIOT/pkg/nimble/Makefile
             sed -i 's|PKG_VERSION.*|PKG_VERSION = '${{ github.sha }}'|' RIOT/pkg/nimble/Makefile
             make -C RIOT/examples/nimble_gatt
