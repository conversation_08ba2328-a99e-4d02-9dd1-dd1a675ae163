# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

syscfg.defs:
    BLE_SVC_ANS_NEW_ALERT_CAT:
        description: "Initial supported new alert category bitmask."
        value: 0

    BLE_SVC_ANS_UNR_ALERT_CAT:
        description: "Initial supported unread alert category bitmask."
        value: 0

    BLE_SVC_ANS_SYSINIT_STAGE:
        description: >
            Sysinit stage for the alert notification service.
        value: 303
