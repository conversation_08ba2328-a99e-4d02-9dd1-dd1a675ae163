# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

syscfg.vals:
    BLE_HS_DEBUG: 1
    BLE_HS_PHONY_HCI_ACKS: 1
    BLE_HS_REQUIRE_OS: 0
    BLE_MAX_CONNECTIONS: 8
    BLE_GATT_MAX_PROCS: 16
    BLE_SM: 1
    BLE_SM_SC: 1
    BLE_SM_CSIS_SIRK: 1
    MSYS_1_BLOCK_COUNT: 100
    BLE_L2CAP_COC_MAX_NUM: 2
    CONFIG_FCB: 1
    BLE_VERSION: 52
    BLE_L2CAP_ENHANCED_COC: 1
    BLE_TRANSPORT_LL: custom
    BLE_EATT_CHAN_NUM: 0
