# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

syscfg.vals:
    BLE_HCI_VS: 1
    BLE_TRANSPORT_ACL_COUNT: 24
    BLE_TRANSPORT_EVT_COUNT: 2
    BLE_HW_WHITELIST_ENABLE: 0
    BLE_LL_CFG_FEAT_DATA_LEN_EXT: 0
    BLE_LL_CFG_FEAT_LE_CSA2: 1
    BLE_LL_CFG_FEAT_LE_ENCRYPTION: 0
    BLE_LL_CFG_FEAT_LL_PRIVACY: 0
    BLE_LL_CONN_INIT_MAX_TX_BYTES: 'MYNEWT_VAL_BLE_LL_MAX_PKT_SIZE'
    BLE_LL_SUPP_MAX_RX_BYTES: 'MYNEWT_VAL_BLE_LL_MAX_PKT_SIZE'
    BLE_LL_SUPP_MAX_TX_BYTES: 'MYNEWT_VAL_BLE_LL_MAX_PKT_SIZE'
    BLE_SM_LEGACY: 0
    BLE_SM_SC: 0
    BLE_MAX_PERIODIC_SYNCS: 0
    MSYS_1_BLOCK_COUNT: 5
    MSYS_1_BLOCK_SIZE: 88
    MCU_LFCLK_SOURCE: LFXO
