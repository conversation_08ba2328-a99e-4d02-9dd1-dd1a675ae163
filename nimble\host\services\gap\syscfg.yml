# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

syscfg.defs:
    BLE_SVC_GAP_DEVICE_NAME:
        description: >
            Default value for "Device Name" characteristics, unless overwritten
            by application.
        value: '"nimble"'
    BLE_SVC_GAP_DEVICE_NAME_WRITE_PERM:
        description: >
            Defines permissions for writing "Device Name" characteristics. Can
            be zero to allow write without extra permissions or combination of:
                BLE_GATT_CHR_F_WRITE_ENC
                BLE_GATT_CHR_F_WRITE_AUTHEN
                BLE_GATT_CHR_F_WRITE_AUTHOR
            Set to '-1' to make characteristic read only.
        value: -1
    BLE_SVC_GAP_DEVICE_NAME_MAX_LENGTH:
        description: Maximum length for "Device Name" characteristics
        value: 31
    BLE_SVC_GAP_APPEARANCE:
        description: 'Device appearance'
        value: 0
    BLE_SVC_GAP_APPEARANCE_WRITE_PERM:
        description: >
            Defines permissions for writing "Appearance" characteristics. Can
            be zero to allow write without extra permissions or combination of:
                BLE_GATT_CHR_F_WRITE_ENC
                BLE_GATT_CHR_F_WRITE_AUTHEN
                BLE_GATT_CHR_F_WRITE_AUTHOR
            Set to '-1' to make characteristic read only.
        value: -1

    # Setting all values for PPCP to '0' will disable characteristic!
    BLE_SVC_GAP_PPCP_MIN_CONN_INTERVAL:
        description: >
            Value of "minimum connection interval" of PPCP characteristic as
            defined by Core specification 5.0, Vol 3, Part C, section 12.3.
        value: 0
    BLE_SVC_GAP_PPCP_MAX_CONN_INTERVAL:
        description: >
            Value of "maximum connection interval" of PPCP characteristic as
            defined by Core specification 5.0, Vol 3, Part C, section 12.3.
        value: 0
    BLE_SVC_GAP_PPCP_SLAVE_LATENCY:
        description: >
            Value of "slave latency" of PPCP characteristic as  defined by Core
            specification 5.0, Vol 3, Part C, section 12.3.
        value: 0
    BLE_SVC_GAP_PPCP_SUPERVISION_TMO:
        description: >
            Value of "connection supervision timeout multiplier" of PPCP
            characteristic as defined by Core specification 5.0, Vol 3, Part C,
            section 12.3.
        value: 0

    BLE_SVC_GAP_CENTRAL_ADDRESS_RESOLUTION:
        description: >
            Value of "Central Address Resolution" characteristics, as defined
            by Core specification 5.0, Vol 3, Part C, section 12.
            Set to '-1' to disable.
        value: -1

    BLE_SVC_GAP_SYSINIT_STAGE:
        description: >
            Sysinit stage for the GAP BLE service.
        value: 301
