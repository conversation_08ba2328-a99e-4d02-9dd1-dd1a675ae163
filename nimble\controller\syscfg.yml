# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

syscfg.defs:
    BLE_CONTROLLER: 1

    BLE_LL_ROLE_CENTRAL:
        description: 'Enables controller support for the Central role.'
        value: MYNEWT_VAL(BLE_ROLE_CENTRAL)
        restrictions:
            - 'BLE_LL_ROLE_OBSERVER if 1'

    BLE_LL_ROLE_PERIPHERAL:
        description: 'Enables controller support for the Peripheral role.'
        value: MYNEWT_VAL(BLE_ROLE_PERIPHERAL)
        restrictions:
            - 'BLE_LL_ROLE_BROADCASTER if 1'

    BLE_LL_ROLE_BROADCASTER:
        description: 'Enables controller support for the Broadcaster role.'
        value: MYNEWT_VAL(BLE_ROLE_BROADCASTER)
        restrictions:
            - 'BLE_LL_ROLE_OBSERVER if 0'

    BLE_LL_ROLE_OBSERVER:
        description: 'Enables controller support for the Observer role.'
        value: MYNEWT_VAL(BLE_ROLE_OBSERVER)
        restrictions:
            - 'BLE_LL_ROLE_BROADCASTER if 0'

    BLE_HW_WHITELIST_ENABLE:
        description: >
            Used to enable hardware white list
        value: 1

    BLE_LL_SYSVIEW:
        description: >
            Enable SystemView tracing module for controller.
        value: 0

    BLE_LL_PRIO:
        description: 'The priority of the LL task'
        type: 'task_priority'
        value: 0

    BLE_LL_SCA:
        description: Sleep clock accuracy of our device (in ppm)
        value: MYNEWT_VAL(BLE_LL_OUR_SCA)
        range: 0..500

    BLE_LL_TX_PWR_DBM:
        description: >
            Default Transmit power level (in dBm). Actual transmit power
            may be rounded up or down depending on used radio.
        value: '0'

    BLE_LL_TX_PWR_MAX_DBM:
        description: >
            Maximum allowed transmit power level (in dBm). This limits maximum
            power to specified value (including FEM and/or host compensation).
            Useful for ensuring selected power class of device. Defaults to
            maximum allowed by specification (Power Class 1).
        value: '20'

    BLE_LL_NUM_COMP_PKT_ITVL_MS:
        description: >
            Determines the interval at which the controller will send the
            number of completed packets event to the host. Rate is in milliseconds.
        value: 2000

    BLE_LL_MANUFACTURER_ID:
        description: >
            Manufacturer ID, as assigned by Bluetooth SIG
        value: MYNEWT_VAL(BLE_LL_MFRG_ID)

    # Configuration items for the number of duplicate advertisers and the
    # number of advertisers from which we have heard a scan response.
    BLE_LL_NUM_SCAN_DUP_ADVS:
        description: 'The number of duplicate advertisers stored.'
        value: '8'
    BLE_LL_NUM_SCAN_RSP_ADVS:
        description: >
            The number of advertisers from which we have heard a scan
            response. Prevents sending duplicate events to host.
        value: '8'

    BLE_LL_WHITELIST_SIZE:
        description: 'Size of the LL whitelist.'
        value: '8'

    BLE_LL_RESOLV_LIST_SIZE:
        description: 'Size of the resolving list.'
        value: '4'

    BLE_LL_CONN_PHY_DEFAULT_PREF_MASK:
        description: >
            Default PHY preference mask used if no HCI LE Set Preferred PHY
            was received.
        value: 0x07
    BLE_LL_CONN_PHY_PREFER_2M:
        description: >
            If enabled, LL will always attempt to switch to 2M PHY if present
            in preferred mask even if active PHY is also allowed by that mask.
            Otherwise LL will not attempt to switch PHY as long as active PHY
            is present in preferred mask.
        value: 0
    BLE_LL_CONN_PHY_INIT_UPDATE:
        description: >
            If enabled, LL will attempt to switch PHY (depending on preferences
            mask set) after connection was established.
        value: 0

    # Data length management definitions for connections. These define the
    # maximum size of the PDU's that will be sent and/or received in a
    # connection.
    BLE_LL_MAX_PKT_SIZE:
        description: 'The maximum PDU size that can be sent/received'
        value: '251'
    BLE_LL_SUPP_MAX_RX_BYTES:
        description: 'The maximum supported received PDU size'
        value: MYNEWT_VAL(BLE_LL_MAX_PKT_SIZE)
    BLE_LL_SUPP_MAX_TX_BYTES:
        description: 'The maximum supported transmit PDU size'
        value: MYNEWT_VAL(BLE_LL_MAX_PKT_SIZE)
    BLE_LL_CONN_INIT_MAX_TX_BYTES:
        description: >
            Used to set the initial maximum transmit PDU size in a
            connection. The host can always tell the controller to update this
            value.
        value: '27'
    BLE_LL_CONN_INIT_AUTO_DLE:
        description: >
            If BLE_LL_CONN_INIT_MAX_TX_BYTES is set to value greater than 27
            controller will automatically attempt to do the data length update
            procedure.
        value: 1

    # The number of slots that will be allocated to each connection
    BLE_LL_CONN_INIT_SLOTS:
        description: >
            This is the number of "slots" allocated to a connection when scheduling
            connections. Each slot is 1.25 msecs long. Note that a connection event may
            last longer than the number of slots allocated here and may also end earlier
            (depending on when the next scheduled event occurs and how much data needs
            to be transferred in the connection). However, you will be guaranteed that
            a connection event will be given this much time, if needed. Consecutively
            scheduled items will be at least this far apart
        value: '4'

    BLE_LL_CONN_INIT_MIN_WIN_OFFSET:
        description: >
            This is the minimum number of "slots" for WindowOffset value used for
            CONNECT_IND when creating new connection as a master. Each slot is 1.25
            msecs long. Increasing this value will delay first connection event after
            connection is created. However, older TI CC254x controllers cannot change
            connection parameters later if WindowOffset was set to 0 in CONNECT_IND. To
            ensure interoperability with such devices set this value to 2 (or more).
        value: '0'

    BLE_LL_CONN_STRICT_SCHED:
        description: >
            Enable connection strict scheduling (css).
            In css mode, connections in central role are scheduled in fixed time
            intervals called periods. Each period is divided into an arbitrary
            number of slots and each connection anchor point is always scheduled
            at slot boundary. This means (assuming only central connections are
            active) it's possible to reliably schedule up to number-of-slots
            connections each at period-duration interval, each connection will
            be allocated at least one slot in each connection event.
        value: 0
    BLE_LL_CONN_STRICT_SCHED_FIXED:
        description: >
            Enable fixed mode for connection strict scheduling, i.e. slot duration
            and slots per period values are fixed to syscfg values and cannot
            be changed in runtime. This allows for some compile-time optimizations.
        value: 0
    BLE_LL_CONN_STRICT_SCHED_SLOT_US:
        description: >
            Slot duration in microseconds. Shall be multiply of 1250us.
        value: 3750
    BLE_LL_CONN_STRICT_SCHED_PERIOD_SLOTS:
        description: >
            Number of slots per period. Duration of slot determines connection
            interval used for each connection in central role.
        value: 8

    # The number of random bytes to store
    BLE_LL_RNG_BUFSIZE:
        description: >
            The number of random bytes that the link layer will try to
            always have available for the host to use. Decreasing this
            value may cause host delays if the host needs lots of random
            material often.
        value: '32'

    BLE_LL_RFMGMT_ENABLE_TIME:
        description: >
            Time required for radio and/or related components to be fully
            enabled before any request from LL is sent. This value is used
            by rfmgmt to enable PHY in advance, before request from LL is
            made. It depends on radio driver selected and may also depend
            on hardware used:
            - nrf51 - time required for XTAL to settle
            - nrf52 - time required for XTAL to settle
            Value is specified in microseconds. If set to 0, rfmgmt keeps
            PHY enabled all the time.
        value: MYNEWT_VAL(BLE_XTAL_SETTLE_TIME)

    BLE_LL_HCI_LLCP_TRACE:
        description: >
            Enables LLCP tracing using HCI vendor-specific events.
        value: '0'

    # Configuration for LL supported features.
    #
    # There are a total 8 features that the LL can support. These can be found
    # in v4.2, Vol 6 Part B Section 4.6.
    #
    # These feature definitions are used to inform a host or other controller
    # about the LL features supported by the controller.
    #
    # NOTE: 'the' controller always supports extended reject indicate and thus
    # is not listed here.


    BLE_LL_CFG_FEAT_LE_ENCRYPTION:
        description: >
            This option enables/disables encryption support in the controller.
            This option saves both both code and RAM.
        value: 'MYNEWT_VAL_BLE_LL_ROLE_CENTRAL || MYNEWT_VAL_BLE_LL_ROLE_PERIPHERAL || MYNEWT_VAL_BLE_LL_ISO'

    BLE_LL_CFG_FEAT_CONN_PARAM_REQ:
        description: >
            This option enables/disables the connection parameter request
            procedure.  This is implemented in the controller but is disabled
            by default.
        value: 'MYNEWT_VAL_BLE_LL_ROLE_CENTRAL || MYNEWT_VAL_BLE_LL_ROLE_PERIPHERAL'

    BLE_LL_CFG_FEAT_PERIPH_INIT_FEAT_XCHG:
        description: >
            This option allows a slave to initiate the feature exchange
            procedure.  This feature is implemented but currently has no impact
            on code or ram size
        value: 'MYNEWT_VAL_BLE_LL_ROLE_CENTRAL || MYNEWT_VAL_BLE_LL_ROLE_PERIPHERAL'

    BLE_LL_CFG_FEAT_LE_PING:
        description: >
            This option allows a controller to send/receive LE pings.
            Currently, this feature is not implemented by the controller so
            turning it on or off has no effect.
        value: 'MYNEWT_VAL_BLE_LL_CFG_FEAT_LE_ENCRYPTION'

    BLE_LL_CFG_FEAT_DATA_LEN_EXT:
        description: >
            This option enables/disables the data length update procedure in
            the controller. If enabled, the controller is allowed to change the
            size of tx/rx pdu's used in a connection. This option has only
            minor impact on code size and non on RAM.
        value: 'MYNEWT_VAL_BLE_LL_ROLE_CENTRAL || MYNEWT_VAL_BLE_LL_ROLE_PERIPHERAL'

    BLE_LL_CFG_FEAT_LL_PRIVACY:
        description: >
            This option is used to enable/disable LL privacy.
        value: '1'

    BLE_LL_CFG_FEAT_LE_CSA2:
        description: >
            This option is used to enable/disable support for LE Channel
            Selection Algorithm #2.
        value: '0'

    BLE_LL_CFG_FEAT_LE_2M_PHY:
        description: >
            This option is used to enable/disable support for the 2Mbps PHY.
        value: MYNEWT_VAL(BLE_PHY_2M)

    BLE_LL_CFG_FEAT_LE_CODED_PHY:
        description: >
            This option is used to enable/disable support for the coded PHY.
        value: MYNEWT_VAL(BLE_PHY_CODED)

    BLE_LL_CFG_FEAT_LL_EXT_ADV:
        description: >
            This option is used to enable/disable support for Extended
            Advertising Feature. That means extended scanner, advertiser
            and connect.
        value: MYNEWT_VAL(BLE_EXT_ADV)

    BLE_LL_CFG_FEAT_LL_PERIODIC_ADV:
        description: >
            This option is used to enable/disable support for Periodic
            Advertising Feature.
        value: MYNEWT_VAL(BLE_PERIODIC_ADV)

    BLE_LL_CFG_FEAT_LL_PERIODIC_ADV_SYNC_CNT:
        description: >
            This option is used to configure number of supported periodic syncs.
        value: MYNEWT_VAL(BLE_MAX_PERIODIC_SYNCS)

    BLE_LL_CFG_FEAT_LL_PERIODIC_ADV_SYNC_LIST_CNT:
        description: >
            Size of Periodic Advertiser sync list.
        value: MYNEWT_VAL(BLE_MAX_PERIODIC_SYNCS)

    BLE_LL_CFG_FEAT_LL_PERIODIC_ADV_SYNC_TRANSFER:
        description: >
            This option is use to enable/disable support for Periodic
            Advertising Sync Transfer Feature.
        value: MYNEWT_VAL(BLE_PERIODIC_ADV_SYNC_TRANSFER)

    BLE_LL_CFG_FEAT_CTRL_TO_HOST_FLOW_CONTROL:
        description: >
            Enable controller-to-host flow control support. This allows host to
            limit number of ACL packets sent at once from controller to avoid
            congestion on HCI transport if feature is also supported by host.
        value: 0
        restrictions:
            - '(BLE_ROLE_CENTRAL || BLE_ROLE_PERIPHERAL) if 1'

    BLE_LL_CFG_FEAT_LL_SCA_UPDATE:
        description: >
            This option is used to enable/disable support for SCA update procedure
        value: 0
        restrictions:
            - '(BLE_VERSION >= 52) if 1'

    BLE_LL_CFG_FEAT_LL_ENHANCED_CONN_UPDATE:
        description: >
            Enables support LE Enhanced Connection Update.
            This allows to use Conenction Subrate Update and Connection Subrate
            Request procedures to modify subrate paramters for a connection.
        value: 0
        restrictions:
            - '(BLE_VERSION >= 53) if 1'

    BLE_LL_PERIODIC_ADV_SYNC_BIGINFO_REPORTS:
        description: >
            This option is used to enable/disable support for
            handling BIGInfo data
        value: MYNEWT_VAL(BLE_PERIODIC_ADV_SYNC_BIGINFO_REPORTS)
        experimental: 1

    BLE_LL_SCAN_AUX_SEGMENT_CNT:
         description: >
            Number of auxiliary advertising segments that can be scanned
            concurrently (Core 5.2, Vol 6, Part B, *******.2).
         value: MYNEWT_VAL(BLE_LL_EXT_ADV_AUX_PTR_CNT)

    BLE_LL_PUBLIC_DEV_ADDR:
        description: >
            Set public device address. Address is specified as 48-bit number.
            If non-zero, this setting has priority over BLE_PUBLIC_DEV_ADDR.
            Note: this setting should only be used for testing purposes, it is
                  not intended for production builds.
        value: 0x000000000000

    BLE_LL_DTM:
        description: >
             Enables HCI Test commands needed for Bluetooth SIG certification
        value: MYNEWT_VAL(BLE_LL_DIRECT_TEST_MODE)
    BLE_LL_DTM_EXTENSIONS:
        description: >
             Enables non-standard extensions to HCI test commands. Once enabled,
             HCI_LE_Transmitter_Test accepts extra parameters in addition to
             those defined in Core specification
               interval (2 octets)   interval between packets (usecs), overrides
                                     standard interval
               pkt_count (2 octets)  number of packets to transmit, controller
                                     will automatically stop sending packets
                                     after given number of packets was sent
             Setting either of these parameters to 0 will configure for default
             behavior, as per Core specification.
             If specified interval is shorter then allowed by specification it
             will be ignored.
             Extended parameters shall immediately follow standard parameters.
             Controller can accept both standard and extended version of command
             depending on specified HCI command length.
        value: 0

    BLE_LL_HCI_VS:
        description: >
            Enables support for vendor-specific HCI commands.
        value: MYNEWT_VAL(BLE_HCI_VS)
    BLE_LL_HCI_VS_CONN_STRICT_SCHED:
        description: >
            Enable HCI commands to control connection strict scheduling.
        value: 0
        restrictions:
            - BLE_LL_HCI_VS if 1
    BLE_LL_HCI_VS_LOCAL_IRK:
        description: >
            Enables HCI command to set local IRK.
            The local IRK is used by controller to generate RPA address in case
            own address type 0x02 or 0x03 was requested by host but there is no
            corresponding entry on resolving list. This allows to handle privacy
            scenarios almost entirely in controller. If no local IRK is set, the
            controller behaves as if feature is not enabled.
        value: 0
        restrictions:
            - BLE_LL_HCI_VS if 1


    BLE_LL_HCI_VS_EVENT_ON_ASSERT:
        description: >
            This options enables controller to send a vendor-specific event on
            an assertion in controller code. The event contains file name and
            line number where assertion occured.
        value: MYNEWT_VAL(BLE_LL_VND_EVENT_ON_ASSERT)

    BLE_FEM_PA:
        description: Enable FEM PA support
        value: MYNEWT_VAL(BLE_LL_PA)
    BLE_FEM_PA_GAIN:
        description: PA fixed TX gain (in dBm).
        value: 0
    BLE_FEM_PA_GAIN_TUNABLE:
        description: >
            PA TX gain is tunable and not constant. If enabled
            ble_fem_pa_tx_power_set() and ble_fem_pa_tx_power_round()
            shall be implemented (see ble_fem.h for details).
        value: 0
    BLE_FEM_PA_GPIO:
        description: >
            GPIO pin number to control PA. Pin is set to high state when PA
            should be enabled.
        value: MYNEWT_VAL(BLE_LL_PA_GPIO)
    BLE_FEM_PA_TURN_ON_US:
        description: >
            Time required for PA to turn on, in microseconds.
        value: MYNEWT_VAL(BLE_LL_PA_TURN_ON_US)
    BLE_FEM_LNA:
        description: Enable LNA support
        value: MYNEWT_VAL(BLE_LL_LNA)
    BLE_FEM_LNA_GAIN:
        description: LNA fixed RX gain (in dBm).
        value: 0
    BLE_FEM_LNA_GAIN_TUNABLE:
        description: >
            LNA RX gain is tunable and not constant. If enabled
            ble_fem_lna_rx_gain() shall be implemented (see ble_fem.h for
            details).
        value: 0
    BLE_FEM_LNA_GPIO:
        description: >
            GPIO pin number to control LNA. Pin is set to high state when LNA
            should be enabled.
        value: MYNEWT_VAL(BLE_LL_LNA_GPIO)
    BLE_FEM_LNA_TURN_ON_US:
        description: >
            Time required for LNA to turn on, in microseconds.
        value: MYNEWT_VAL(BLE_LL_LNA_TURN_ON_US)
    BLE_FEM_ANTENNA:
        description: >
            Enable support for runtime antenna selection in FEM.
        value: 0

    BLE_LL_ISO:
        description: >
            Enable support for isochronous data.
            This enables common code (e.g. ISOAL) for all types of isochronous
            data, particular ISO features have to be enabled separately.
        restrictions:
            - (BLE_VERSION >= 52) if 1
        value: MYNEWT_VAL(BLE_ISO)
        state: experimental
    BLE_LL_ISO_BROADCASTER:
        description: >
            Enable support for Isochronous Broadcasting state.
        restrictions:
            - BLE_LL_ISO if 1
        value: MYNEWT_VAL(BLE_ISO_BROADCASTER)
        state: experimental
    BLE_LL_ISO_HCI_FEEDBACK_INTERVAL_MS:
        description: >
            Enables ISO synchronization feedback using vendor-specific HCI event.
            The event is sent at configured interval after completed ISO event
            and contains BIG handle, number of expected SDUs per ISO interval
            and difference between expected vs actual number of SDUs queued in
            controller.
            The expected number of SDUs queued after each event is number of
            SDUs required for each ISO event (i.e. including pre-transmissions)
            minus number of SDUs expected per each ISO interval.
            The host can use feedback to e.g. adjust for jitter between audio
            clock and LL clock.
            Set to 0 to disable.
        value: 0
        experimental: 1
        restrictions:
            - BLE_LL_HCI_VS || BLE_LL_ISO_HCI_FEEDBACK_INTERVAL_MS == 0
    BLE_LL_ISO_HCI_DISCARD_THRESHOLD:
        description: >
            Enables automatic discarding of excessive ISO SDUs to avoid exhaustion
            of HCI ISO buffers in case host sends too many SDUs.
            Threshold is defined as number of ISO events. If number of queued
            SDUs exceeds number of SDUs required for single event (i.e. including
            pre-transmissions) and number of subsequent ISO events defined by
            threshold value, the controller will drop any excessive SDUs and
            notify to host as if they were already sent.
            Set to 0 to disable.
        value: 0
        experimental: 1

    BLE_LL_SYSINIT_STAGE:
        description: >
            Sysinit stage for the NimBLE controller.
        value: 250

    BLE_LL_DEBUG_GPIO_HCI_CMD:
        description: >
            GPIO pin number to debug HCI commands flow. Pin is set to high state
            when HCI command is being processed.
        value: -1
    BLE_LL_DEBUG_GPIO_HCI_EV:
        description: >
            GPIO pin number to debug HCI events flow. Pin is set to high state
            when HCI event is being sent.
        value: -1
    BLE_LL_DEBUG_GPIO_SCHED_RUN:
        description: >
            GPIO pin number to debug scheduler running (on timer). Pin is set
            to high state while scheduler is running.
        value: -1
    BLE_LL_DEBUG_GPIO_SCHED_ITEM:
        description: >
            GPIO pin number to debug scheduler item execution times. Pin is set
            to high state while item is active.
        value: -1
    BLE_LL_DEBUG_GPIO_RFMGMT:
        description: >
            GPIO pin number to debug rfmgmt activity. Pin is set to high state
            while rfmgmt is active.
        value: -1

    BLE_LL_EXT:
        description: >
            Enables API to support external (i.e. non-native to LL) state for
            NimBLE LL and scheduler. See ble_ll_ext.h.
        experimental: 1
        value: 0
# Below settings allow to change scheduler timings. These should be left at
# default values unless you know what you are doing!
    BLE_LL_SCHED_AUX_MAFS_DELAY:
        description: >
            Additional delay [us] between last ADV_EXT_IND and AUX_ADV_IND PDUs
            when scheduling extended advertising event. This extends T_MAFS.
        value: 0
    BLE_LL_SCHED_AUX_CHAIN_MAFS_DELAY:
        description: >
            Additional delay [us] between consecutive AUX_CHAIN_IND PDUs
            when scheduling extended or periodic advertising event. This extends
            T_MAFS.
        value: 0
    BLE_LL_SCHED_SCAN_AUX_PDU_LEN:
        description: >
            This is expected PDU len for AUX_ADV_IND and subsequent
            AUX_CHAIN_IND. When scheduling scan scheduler will reserve time for
            receiving this amount of time. Setting this to high value improves
            reception of large PDUs but results in wasting scheduler space when
            receiving small PDUs only. On the other hand too low value can
            result in not being able to scan whole PDU due to being preempted
            by next scheduled item. By default size matching legacy ADV_IND PDU
            payload is used: ExtHeader (Flags, AdvA, ADI) + 31 bytes of data.
        range: 1..257
        value: 41

    BLE_LL_SCHED_SCAN_SYNC_PDU_LEN:
        description: >
            This is expected PDU len for AUX_SYNC_IND and subsequent
            AUX_CHAIN_IND. When scheduling scan scheduler will reserve time for
            receiving this amount of time. Setting this to high value improves
            reception of large PDUs but results in wasting scheduler space when
            receiving small PDUs only. On the other hand too low value can
            result in not being able to scan whole PDU due to being preempted
            by next scheduled item. By default size matching PDU with legacy
            data size is used: ExtHeader + 31 bytes of data.
        range: 1..257
        value: 32

    BLE_LL_CONN_EVENT_END_MARGIN:
        description: >
            Extra time needed for scheduling next connection event. Setting this
            value results in ending connection event sooner (in microseconds)
            which gives more time to schedule next event. This value should be
            tuned only after measuring performance as depends on various factors
            like  build optimisation, cache, clock speed etc.
        value: 0

    BLE_LL_STACK_SIZE:
        description: >
              This is the stack size for LL task.
        value: 120

syscfg.defs.'BLE_LL_CFG_FEAT_LE_2M_PHY || BLE_LL_CFG_FEAT_LE_CODED_PHY':
    BLE_LL_PHY: 1

syscfg.vals.BLE_LL_CFG_FEAT_LL_EXT_ADV:
    BLE_LL_CFG_FEAT_LE_CSA2: 1
    BLE_HW_WHITELIST_ENABLE: 0
    BLE_LL_SCAN_AUX_SEGMENT_CNT: 8

syscfg.vals.BLE_LL_ISO_BROADCASTER:
    BLE_LL_STACK_SIZE: 180

# Enable vendor event on assert in standalone build to make failed assertions in
# controller code visible when connected to external host
syscfg.vals.'!BLE_HOST && !BABBLESIM':
    BLE_LL_HCI_VS_EVENT_ON_ASSERT: 1

syscfg.restrictions:
    - BLE_TRANSPORT_LL == "native"
    - BLE_LL_PUBLIC_DEV_ADDR <= 0xffffffffffff
    - BLE_FEM_PA == 0 || BLE_FEM_PA_GPIO >= 0
    - BLE_FEM_LNA == 0 || BLE_FEM_LNA_GPIO >= 0

$import:
    # defunct and deprecated settings
    - "@apache-mynewt-nimble/nimble/controller/syscfg.defunct.yml"
    # "Here be dragons" settings
    - "@apache-mynewt-nimble/nimble/controller/syscfg.hbd.yml"
