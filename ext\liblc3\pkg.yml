#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

pkg.name: ext/liblc3
pkg.description: LC3 Codec library
pkg.author: "Apache Mynewt <<EMAIL>>"
pkg.homepage: "http://mynewt.apache.org/"
pkg.keywords:
    - lc3

pkg.type: sdk

pkg.cflags: -O3 -std=c11 -ffast-math -Wno-array-bounds
pkg.lflags: -lm

pkg.ign_dirs:
    - "@liblc3/test"
    - "@liblc3/zephyr"
    - "@liblc3/fuzz"
    - "@liblc3/tables"
    - "@liblc3/tools"

pkg.src_dirs:
    - "@liblc3/src"

pkg.include_dirs:
    - "@liblc3/include"

repository.liblc3:
    type: github
    vers: v1.0.4-commit
    branch: main
    user: google
    repo: liblc3
