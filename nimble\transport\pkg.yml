#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

pkg.name: nimble/transport
pkg.description: Meta-package for NimBLE HCI transport
pkg.author: "Apache Mynewt <<EMAIL>>"
pkg.homepage: "http://mynewt.apache.org/"
pkg.keywords:
    - ble
    - bluetooth

pkg.apis:
    - ble_transport

pkg.deps.'BLE_TRANSPORT_HS == "native"':
    - nimble/host
pkg.deps.'BLE_TRANSPORT_LL == "native"':
    - nimble/controller
pkg.deps.'BLE_TRANSPORT_LL == "emspi"':
    - nimble/transport/emspi
pkg.deps.'BLE_TRANSPORT_HS == "dialog_cmac" || BLE_TRANSPORT_LL == "dialog_cmac"':
    - nimble/transport/dialog_cmac
pkg.deps.'BLE_TRANSPORT_HS == "nrf5340" || BLE_TRANSPORT_LL == "nrf5340"':
    - nimble/transport/nrf5340
pkg.deps.'BLE_TRANSPORT_LL == "socket"':
    - nimble/transport/socket
pkg.deps.'BLE_TRANSPORT_HS == "uart"':
    - nimble/transport/uart
pkg.deps.'BLE_TRANSPORT_HS == "usb"':
    - nimble/transport/usb
pkg.deps.'BLE_TRANSPORT_HS == "cdc"':
    - nimble/transport/cdc
pkg.deps.'BLE_TRANSPORT_LL == "apollo3"':
    - nimble/transport/apollo3
pkg.deps.'BLE_TRANSPORT_LL == "uart_ll"':
    - nimble/transport/uart_ll

pkg.deps.BLE_MONITOR_RTT:
    - "@apache-mynewt-core/hw/drivers/rtt"

pkg.init:
    ble_transport_init: 250
    ble_transport_hs_init: 251
    ble_transport_ll_init:
        - $after:ble_transport_hs_init

pkg.init.'BLE_MONITOR_RTT || BLE_MONITOR_UART':
    ble_monitor_init: $before:ble_transport_init
