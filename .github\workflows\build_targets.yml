#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

name: Build check

on: [push, pull_request]

jobs:
  targets:
    name: Build all test targets
    strategy:
      fail-fast: false
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
    runs-on: ${{ matrix.os }}
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-go@v3
        with:
          go-version: 'stable'
      - uses: carlosperate/arm-none-eabi-gcc-action@48db4484a55750df7a0ccca63347fcdea6534d78
        with:
          release: '12.2.Rel1'
      - name: Install Dependencies
        shell: bash
        if: matrix.os == 'ubuntu-latest'
        run: |
             sudo apt-get update
             sudo apt-get install -y gcc-multilib
      - name: Install newt
        shell: bash
        run: |
             go version
             go install mynewt.apache.org/newt/newt@latest
      - name: Setup project
        shell: bash
        run: |
             newt new build
             cp -f .github/project.yml build/project.yml
             cd build
             newt upgrade --shallow=1
             rm -rf targets
             rm -rf repos/apache-mynewt-nimble
             git clone .. repos/apache-mynewt-nimble
             cd ..
      - name: Build targets
        shell: bash
        run: |
             cp -r .github/targets build/targets
             cd build
             ls targets | xargs -n1 sh -c 'echo "Testing $0"; newt build -q $0'
             rm -rf targets
             cd ..
      - name: Build Linux-only targets
        shell: bash
        if: matrix.os == 'ubuntu-latest'
        run: |
             cp -r .github/targets_linux build/targets
             cd build
             ls targets | xargs -n1 sh -c 'echo "Testing $0"; newt build -q  $0'
             rm -rf targets
             cd ..
