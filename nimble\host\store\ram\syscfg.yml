# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.

syscfg.defs:
    BLE_STORE_RAM_SYSINIT_STAGE:
        description: >
            Sysinit stage for the RAM BLE store.
        value: 500
    BLE_STORE_RAM_DEPRECATED_FLAG:
        description: >
            Package store/ram is deprecated and store/config shall be used with BLE_STORE_CONFIG_PERSIST set to 0
        value: 0
        deprecated: 1


syscfg.vals:
    BLE_STORE_RAM_DEPRECATED_FLAG: 1

