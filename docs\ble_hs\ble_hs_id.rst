NimBLE Host Identity Reference
------------------------------

Introduction
~~~~~~~~~~~~

The identity API provides facilities for querying and configuring your device's addresses. BLE's addressing scheme is
quite involved; the summary that follows is only a brief introduction.

BLE defines four address types:

+---------------------------------+---------------------------------------------------------------------------------------------------+-------------+----------------------------------------------+
| Type                            | Description                                                                                       | Identity?   | Configured with                              |
+=================================+===================================================================================================+=============+==============================================+
| Public                          | Address assigned by manufacturer; the three most significant bytes form the manufacturer's OUI.   | Yes         | N/A; read from controller at startup.        |
+---------------------------------+---------------------------------------------------------------------------------------------------+-------------+----------------------------------------------+
| Static random                   | Randomly generated address.                                                                       | Yes         | *ble_hs_id_set_rnd()*                        |
+---------------------------------+---------------------------------------------------------------------------------------------------+-------------+----------------------------------------------+
| Resolvable private (RPA)        | Address randomly generated from an identity address and an identity resolving key (IRK).          | No          | N/A; generated by controller periodically.   |
+---------------------------------+---------------------------------------------------------------------------------------------------+-------------+----------------------------------------------+
| Non-resolvable private (NRPA)   | Randomly generated address.                                                                       | No          | *ble_hs_id_set_rnd()*                        |
+---------------------------------+---------------------------------------------------------------------------------------------------+-------------+----------------------------------------------+

Identity Addresses
^^^^^^^^^^^^^^^^^^

The third column in the above table indicates the *identity* property of each address type. An identity address never
changes, and a device can be identified by one of its unique identity addresses.

Non-identity addresses are used by devices supporting BLE privacy. A device using the privacy feature frequently changes
its own address to a newly-generated non-identity address. By cycling its address, the device makes it impossible for
eavesdroppers to track its location.

A device can have up to two identity addresses at once: one public and one static random. As indicated in the above table,
the public identity address cannot be configured; the static random identity address can be set by calling *ble_hs_id_set_rnd()*.

The address type is selected on a per-GAP-procedure basis. Each time you initiate a GAP procedure, you indicate which
address type the device should use for the duration of the procedure.

Header
~~~~~~

.. code-block:: cpp

    #include "host/ble_hs.h"
