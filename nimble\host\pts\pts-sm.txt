PTS test results for SM

PTS version: 7.5.0
Tested: 07-Oct-2019

syscfg.vals:
    BLE_EXT_ADV: 1
    BLE_PUBLIC_DEV_ADDR: "((uint8_t[6]){0x01, 0xff, 0xff, 0xc0, 0xde, 0xc0})"
    BLE_SM_LEGACY: 1
    BLE_SM_SC: 1
    BLE_L2CAP_COC_MAX_NUM: 5
    BLE_SVC_GAP_PPCP_MIN_CONN_INTERVAL: 9
    BLE_SVC_GAP_PPCP_MAX_CONN_INTERVAL: 30
    BLE_SVC_GAP_PPCP_SUPERVISION_TMO: 2000
    CONSOLE_HISTORY_SIZE: 10

Results:
PASS	test passed
FAIL	test failed
INC		test is inconclusive
N/A		test is disabled due to PICS setup
NONE	test result is none

-------------------------------------------------------------------------------
Test Name		Result	Notes
-------------------------------------------------------------------------------

SM/MAS/PROT/BV-01-C	PASS	connect peer_addr=<addr>
				security-set-data bonding=1 sc=1 our_key_dist=7 their_key_dist=7
				security-pair conn=<handle>
-------------------------------------------------------------------------------

SM/MAS/JW/BV-01-C	N/A
SM/MAS/JW/BV-05-C	PASS	connect peer_addr=<addr>
				security-pair conn=<handle>
				disconnect conn=<handle>
                                <repeat>
SM/MAS/JW/BI-01-C	PASS	connect peer_addr=<addr>
				security-pair conn=<handle>
SM/MAS/JW/BI-04-C	PASS	connect peer_addr=<addr>
				security-set-data bonding=1 sc=1
				security-pair conn=<handle>
-------------------------------------------------------------------------------

SM/MAS/PKE/BV-01-C	PASS	security-set-data io_capabilities=1
				connect peer_addr=<addr>
				b sec pair conn=<handle>
				b passkey conn=<handle> action=3 key=123456
				Note: enter '123456' passkey in PTS
SM/MAS/PKE/BV-04-C	PASS	security-set-data bonding=1 oob_flag=0
				connect peer_addr=<addr>
				security-pair conn=<handle>
				disconnect conn=<handle>
SM/MAS/PKE/BI-01-C	PASS	ecurity-set-data io_capabilities=1 oob_flag=0
				connect peer_addr=<addr>
				security-pair conn=<handle>
				auth-passkey conn=<handle> action=3 key=123456
				Note: enter invalid passkey
SM/MAS/PKE/BI-02-C	PASS	security-set-data io_capabilities=1 oob_flag=0
				connect peer_addr=<addr>
				security-pair conn=<handle>
				auth-passkey conn=<handle> action=3 key=123456
				Note: enter '123456' passkey in PTS
-------------------------------------------------------------------------------

SM/MAS/OOB/BV-01-C	N/A
SM/MAS/OOB/BV-03-C	N/A
SM/MAS/OOB/BV-05-C	PASS	security-set-data io_capabilities=1 oob_flag=0
				connect-peer_addr=<addr>
				security-pair conn=<handle>
				auth-passkey conn=<handle> action=3 key=123456
				Note: enter '123456' passkey in PTS
				disconnect conn=1
SM/MAS/OOB/BV-07-C	PASS	ecurity-set-data io_capabilities=1 oob_flag=0
				connect-peer_addr=<addr>
				security-pair conn=<handle>
				disconnect conn=1
SM/MAS/OOB/BV-09-C	N/A
SM/MAS/OOB/BI-01-C	N/A
-------------------------------------------------------------------------------

SM/MAS/EKS/BV-01-C	PASS	connect peer_addr=<addr>
				security-pair conn=<handle>
				disconnect conn=1
SM/MAS/EKS/BI-01-C	PASS	connect peer_addr=<addr>
				security-pair conn=<handle>
				disconnect conn=1
-------------------------------------------------------------------------------

SM/MAS/SIGN/BV-01-C	N/A
SM/MAS/SIGN/BV-03-C	N/A
SM/MAS/SIGN/BI-01-C	N/A
-------------------------------------------------------------------------------

SM/MAS/KDU/BV-04-C	PASS	security-set-data our_key_dist=4
				connect peer_addr=<addr>
				security-pair conn=<handle>
				disconnect conn=1
SM/MAS/KDU/BV-05-C	PASS	security-set-data our_key_dist=2
				connect peer_addr=<addr>
				security-pair conn=<handle>
				disconnect conn=1
SM/MAS/KDU/BV-06-C	PASS	security-set-data our_key_dist=1
				connect peer_addr=<addr>
				security-pair conn=<handle>
				disconnect conn=1
SM/MAS/KDU/BV-10-C	PASS	security-set-data our_key_dist=2 sc=1
				connect peer_addr=<addr>
				security-pair conn=<handle>
				disconnect conn=1
SM/MAS/KDU/BV-11-C	PASS	security-set-data our_key_dist=2 sc=1
				connect peer_addr=<addr>
				security-pair conn=<handle>
				disconnect conn=1
SM/MAS/KDU/BI-01-C	PASS	connect peer_addr=<addr>
				disconnect conn=1
				reset device
				<OK>
				<repeat>
-------------------------------------------------------------------------------

SM/MAS/SIP/BV-02-C	PASS	security-set-data io_capabilities=4
				connect peer_addr=<addr>
				disconnect conn=1
-------------------------------------------------------------------------------
SM/MAS/SCJW/BV-01-C	PASS	security-set-data sc=1
				connect peer_addr=<addr>
				security-pair conn=<handle>
				disconnect conn=1
SM/MAS/SCJW/BV-04-C	PASS	security-set-data sc=1 io_capabilities=1 our_key_dist=1
				connect peer_addr=<addr>
				security-pair conn=<handle>
				disconnect conn=1
SM/MAS/SCJW/BI-01-C	PASS	security-set-data sc=1 io_capabilities=4
				connect peer_addr=<addr>
				security-pair conn=<handle>
				disconnect conn=1
				<repeat>
				<confirm key number with PTS>
-------------------------------------------------------------------------------

SM/MAS/SCPK/BV-01-C	PASS	security-set-data sc=1 io_capabilities=2
				connect peer_addr=<addr>
				security-pair conn=<handle>
				auth_passkey conn=1 action=2 key=123456
				Note: enter '123456' passkey in PTS
				disconnect conn=1
SM/MAS/SCPK/BV-04-C	PASS	security-set-data io_capabilities=4 oob_flag=0 our_key_dist=1 their_key_dist=1
				connect peer_addr=<addr>
				security-pair conn=<handle>
				auth_passkey conn=1 action=2 key=123456
				Note: enter '123456' passkey in PTS
				disconnect conn=1
SM/MAS/SCPK/BI-01-C	PASS	security-set-data io_capabilities=4 oob_flag=0 sc=1
				connect peer_addr=<addr>
				security-pair conn=<handle>
				disconnect conn=1
				<repeat>
				auth_passkey conn=1 action=2 key=123456
				Note: enter '123456' passkey in PTS
				disconnect conn=1
SM/MAS/SCPK/BI-02-C	PASS	security-set-data io_capabilities=2 oob_flag=0 bonding=1 sc=1
				connect peer_addr=<addr>
				security-pair conn=<handle>
				auth_passkey conn=1 action=2 key=123456
				Note: enter '123456' passkey in PTS
				disconnect conn=1
-------------------------------------------------------------------------------

SM/MAS/SCOB/BV-01-C	N/A
SM/MAS/SCOB/BI-04-C	N/A
SM/MAS/SCOB/BV-01-C	N/A
SM/MAS/SCOB/BI-04-C	N/A
-------------------------------------------------------------------------------

SM/MAS/SCCT/BV-01-C	N/A
SM/MAS/SCCT/BV-03-C	N/A
SM/MAS/SCCT/BV-05-C	N/A
SM/MAS/SCCT/BV-07-C	N/A
SM/MAS/SCCT/BV-09-C	N/A
-------------------------------------------------------------------------------

SM/SLA/PROT/BV-02-C	PASS	advertise-configure connectable=1 legacy=1
				advertise-start
				<wait>
-------------------------------------------------------------------------------

SM/MAS/JW/BV-02-C	PASS	advertise-configure connectable=1 legacy=1
				advertise-start
SM/SLA/JW/BI-02-C	PASS	advertise-configure connectable=1 legacy=1
				advertise-start
SM/SLA/JW/BI-03-C	PASS	advertise-configure connectable=1 legacy=1
				advertise-start
-------------------------------------------------------------------------------

SM/SLA/PKE/BV-02-C	PASS	security-set-data io_capabilities=4
				advertise-configure connectable=1 legacy=1
				advertise-start
				auth-passkey conn=<handle> action=2 key=<key>
				<OK>
SM/SLA/PKE/BV-05-C	PASS	security-set-data io_capabilities=4
				advertise-configure connectable=1 legacy=1
				advertise-start
SM/SLA/PKE/BI-03-C	PASS	security-set-data io_capabilities=4
				advertise-configure connectable=1 legacy=1
				advertise-start
				auth-passkey conn=<handle> action=3 key=123456
				Note: enter invalid passkey
-------------------------------------------------------------------------------

SM/SLA/OOB/BV-02-C	N/A
SM/SLA/OOB/BV-04-C	N/A
SM/SLA/OOB/BV-06-C	PASS	security-set-data io_capabilities=1
				advertise-configure connectable=1 legacy=1
				advertise-start
				auth-passkey conn=<handle> action=3 key=<key>
SM/SLA/OOB/BV-08-C	PASS	advertise-configure connectable=1 legacy=1
				advertise-start
SM/SLA/OOB/BV-10-C	N/A
SM/SLA/OOB/BI-02-C	N/A
-------------------------------------------------------------------------------

SM/SLA/EKS/BV-02-C	PASS	advertise-configure connectable=1 legacy=1
				advertise-start
SM/SLA/EKS/BI-02-C	PASS	advertise-configure connectable=1 legacy=1
				advertise-start
-------------------------------------------------------------------------------

SM/SLA/KDU/BV-01-C	PASS	security-set-data io_capabilities=1
				advertise-configure connectable=1 legacy=1
				advertise-start
SM/SLA/KDU/BV-02-C	PASS	security-set-data io_capabilities=2
				advertise-configure connectable=1 legacy=1
				advertise-start
SM/SLA/KDU/BV-03-C	PASS	security-set-data io_capabilities=4
				advertise-configure connectable=1 legacy=1
				advertise-start
SM/SLA/KDU/BV-07-C	PASS	security-set-data our_key_dist=1 bonding=1
				advertise-configure connectable=1 legacy=1
				advertise-start
SM/SLA/KDU/BV-08-C	PASS	security-set-data our_key_dist=2 sc=1
				advertise-configure connectable=1 legacy=1
				advertise-start
SM/SLA/KDU/BV-09-C	PASS	security-set-data our_key_dist=4 bonding=0
				advertise-configure connectable=1 legacy=1
				advertise-start
SM/SLA/KDU/BI-01-C	PASS	advertise-configure connectable=1 legacy=1
				security-set-data sc=1
				advertise-start
				<reset device>
				<repeat>
-------------------------------------------------------------------------------

SM/SLA/SIP/BV-01-C	PASS	security-set-data io_capabilities=4
				advertise-configure connectable=1 legacy=1
				advertise-start
				security-start conn=<handle>
-------------------------------------------------------------------------------

SM/SLA/SIE/BV-01-C	PASS	security-set-data io_capabilities=3 bonding=1 our_key_dist=1 their_key_dist=1
				advertise-configure connectable=1 legacy=1
				advertise-start
				advertise-start
				security-start conn=<handle>
-------------------------------------------------------------------------------

SM/SLA/SCJW/BV-02-C	PASS	security-set-data io_capabilities=4 oob_flag=0 bonding=0 mitm_flag=0 sc=1 our_key_dist=1
				advertise-configure connectable=1 legacy=1
				advertise-start
SM/SLA/SCJW/BV-03-C	PASS	security-set-data io_capabilities=1 our_key_dist=1 oob_flag=0
				advertise-configure connectable=1 legacy=1
				advertise-start
SM/SLA/SCJW/BI-02-C	PASS	security-set-data io_capabilities=1 oob_flag=0 bonding=1 sc=1
				advertise-configure connectable=1 legacy=1
				advertise-start
-------------------------------------------------------------------------------

SM/SLA/SCPK/BV-02-C	PASS	security-set-data io_capabilities=1 oob_flag=0 sc=1
				advertise-configure connectable=1 legacy=1
				advertise-start
				auth-passkey conn=1 action=4 key=186900 yesno=yy
SM/SLA/SCPK/BV-03-C	PASS	security-set-data io_capabilities=2 oob_flag=0 our_key_dist=1 their_key_dist=1 sc=1
				advertise-configure connectable=1 legacy=1
				advertise-start
				auth-passkey conn=1 action=2 key=123456
				Note: enter '123456' passkey in PTS
SM/SLA/SCPK/BI-03-C	PASS	security-set-data io_capabilities=2 oob_flag=0 sc=1
				advertise-configure connectable=1 legacy=1
				advertise-start
				auth-passkey conn=1 action=2 key=123456 oob=<xx:xx:xx...>
				Note: enter '123456' passkey in PTS
SM/SLA/SCPK/BI-04-C	PASS	security-set-data io_capabilities=2 oob_flag=0 mitm_flag=1 sc=1
				advertise-configure connectable=1 legacy=1
				advertise-start
				<repeat>
				auth-passkey conn=1 action=2 key=123456
				Note: enter '123456' passkey in PTS
-------------------------------------------------------------------------------

SM/SLA/SCOB/BV-02-C	N/A
SM/SLA/SCOB/BI-03-C	N/A
SM/SLA/SCOB/BV-02-C	N/A
SM/SLA/SCOB/BI-03-C	N/A
-------------------------------------------------------------------------------

SM/SLA/SCCT/BV-02-C	N/A
SM/SLA/SCCT/BV-04-C	N/A
SM/SLA/SCCT/BV-06-C	N/A
SM/SLA/SCCT/BV-08-C	N/A
SM/SLA/SCCT/BV-10-C	N/A