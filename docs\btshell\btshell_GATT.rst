GATT feature API for btshell
============================

GATT(GENERIC ATTRIBUTE PROFILE) describes a service framework using the Attribute Protocol for discovering services,
and for reading and writing characteristic values on a peer device. There are 11 features defined in the GATT Profile,
and each of the features is mapped to procedures and sub-procedures:

Available commands
~~~~~~~~~~~~~~~~~~

Parameters default values (if applicable) are marked red.

Configuration
-------------

+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
| **Command**                        | **Parmeters**   | \*\* Possible values\*\*   | **Description**                                           |
+====================================+=================+============================+===========================================================+
| **gatt-discover-characteristic**   |                 |                            | Discover GATT characteristics                             |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
|                                    | conn            | [UINT16]                   | Connection handle                                         |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
|                                    | uuid            | [UINT16]                   | Characteristic UUID                                       |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
|                                    | start           | [UINT16]                   | Discovery start handle                                    |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
|                                    | end             | [UINT16]                   | Discovery end handle                                      |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
| **gatt-discover-descriptor**       |                 |                            | Discover GATT descriptors                                 |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
|                                    | conn            | [UINT16]                   | Connection handle                                         |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
|                                    | start           | [UINT16]                   | Discovery start handle                                    |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
|                                    | end             | [UINT16]                   | Discovery end handle                                      |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
| **gatt-discover-service**          |                 |                            | Discover services                                         |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
|                                    | conn            | [UINT16]                   | Connection handle                                         |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
|                                    | uuid16          | [UINT16]                   | Service UUID                                              |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
| **gatt-discover-full**             |                 |                            | Discover services, characteristic and descriptors         |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
|                                    | conn            | [UINT16]                   | Connection handle                                         |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
| **gatt-find-included-services**    |                 |                            | Find included services                                    |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
|                                    | conn            | [UINT16]                   | Connection handle                                         |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
|                                    | start           | [UINT16]                   | Discovery start handle                                    |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
|                                    | end             | [UINT16]                   | Discovery end handle                                      |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
| **gatt-exchange-mtu**              |                 |                            | Initiate ATT MTU exchange procedure                       |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
|                                    | conn            | [UINT16]                   | Connection handle                                         |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
| **gatt-read**                      |                 |                            | Read attribute                                            |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
|                                    | conn            | [UINT16]                   | Connection handle                                         |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
|                                    | long            | [``0``-1]                  | Long read                                                 |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
|                                    | attr            | [UINT16]                   | Attribute handle                                          |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
|                                    | offset          | [UINT16]                   | Long read offset value                                    |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
|                                    | uuid            | [UINT16]                   | Characteristic UUID                                       |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
|                                    | start           | [UINT16]                   | Discovery start handle                                    |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
|                                    | end             | [UINT16]                   | Discovery end handle                                      |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
| **gatt-notify**                    |                 |                            | Send notification or indication to all subscribed peers   |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
|                                    | attr            | [UINT16]                   | Attribute handle                                          |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
| **gatt-service-changed**           |                 |                            | Send Services Changed notification                        |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
|                                    | start           | [UINT16]                   | Start handle                                              |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
|                                    | end             | [UINT16]                   | End handle                                                |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
| **gatt-service-visibility**        |                 |                            | Set service visibility                                    |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
|                                    | handle          | [UINT16]                   | Service handle                                            |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
|                                    | visibility      | [``0``-1]                  | Service visibility                                        |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
| **gatt-show**                      |                 |                            | Show remote devices discovered databases structure        |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
| **gatt-show-local**                |                 |                            | Show local database structure                             |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
| **gatt-write**                     |                 |                            | Write attribute                                           |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
|                                    | conn            | [UINT16]                   | Connection handle                                         |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
|                                    | no\_rsp         | [``0``-1]                  | Use Write Without Response                                |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
|                                    | long            | [``0``-1]                  | Use Long Write procedure                                  |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
|                                    | attr            | [UINT16]                   | Attribute handle                                          |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
|                                    | offset          | [UINT16]                   | Long write offset value                                   |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
|                                    | value           | XX:XX:XX...                | Data to write                                             |
+------------------------------------+-----------------+----------------------------+-----------------------------------------------------------+
