#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

name: Unit tests

on: [push, pull_request]

jobs:
  newt_test:
    name: Run newt test all
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-go@v3
        with:
          go-version: 'stable'
      - name: Install Dependencies
        shell: bash
        run: |
             sudo apt-get update
             sudo apt-get install -y gcc-multilib
      - name: Install newt
        shell: bash
        run: |
             go version
             go install mynewt.apache.org/newt/newt@latest
      - name: Setup project
        shell: bash
        run: |
             newt new build
             cp -f .github/project.yml build/project.yml
             cd build
             newt upgrade --shallow=1
             rm -rf repos/apache-mynewt-nimble
             git clone .. repos/apache-mynewt-nimble
             cd ..
      - name: newt test all
        shell: bash
        run: |
             cd build
             newt test all
