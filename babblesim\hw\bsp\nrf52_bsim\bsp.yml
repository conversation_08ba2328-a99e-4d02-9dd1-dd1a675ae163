#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

bsp.name: "nRF52 DK"
bsp.url: https://www.nordicsemi.com/Software-and-Tools/Development-Kits/nRF52-DK
bsp.maker: "Nordic Semiconductor"
bsp.arch: bsim_arch
bsp.compiler: "@apache-mynewt-core/compiler/sim"
bsp.downloadscript: "hw/bsp/nrf52_bsim/nordic_pca10040_download.sh"
bsp.debugscript: "hw/bsp/nrf52_bsim/nordic_pca10040_debug.sh"
bsp.downloadscript.WINDOWS.OVERWRITE: "hw/bsp/nrf52_bsim/nordic_pca10040_download.cmd"
bsp.debugscript.WINDOWS.OVERWRITE: "hw/bsp/nrf52_bsim/nordic_pca10040_debug.cmd"

bsp.flash_map:
    areas:
        # System areas.
        FLASH_AREA_BOOTLOADER:
            device: 0
            offset: 0x00000000
            size: 16kB
        FLASH_AREA_IMAGE_0:
            device: 0
            offset: 0x00008000
            size: 232kB
        FLASH_AREA_IMAGE_1:
            device: 0
            offset: 0x00042000
            size: 232kB
        FLASH_AREA_IMAGE_SCRATCH:
            device: 0
            offset: 0x0007c000
            size: 4kB

        # User areas.
        FLASH_AREA_REBOOT_LOG:
            user_id: 0
            device: 0
            offset: 0x00004000
            size: 16kB
        FLASH_AREA_NFFS:
            user_id: 1
            device: 0
            offset: 0x0007d000
            size: 12kB
