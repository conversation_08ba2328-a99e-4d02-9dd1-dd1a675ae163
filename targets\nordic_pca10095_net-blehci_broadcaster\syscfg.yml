#
# Licensed to the Apache Software Foundation (ASF) under one
# or more contributor license agreements.  See the NOTICE file
# distributed with this work for additional information
# regarding copyright ownership.  The ASF licenses this file
# to you under the Apache License, Version 2.0 (the
# "License"); you may not use this file except in compliance
# with the License.  You may obtain a copy of the License at
#
#  http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing,
# software distributed under the License is distributed on an
# "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
# KIND, either express or implied.  See the License for the
# specific language governing permissions and limitations
# under the License.
#

syscfg.vals:
    BLE_TRANSPORT_HS: nrf5340

    MSYS_1_BLOCK_COUNT: 12
    MSYS_1_BLOCK_SIZE: 292
    BLE_LL_CFG_FEAT_DATA_LEN_EXT: 1
    BLE_PHY_2M: 1
    BLE_PHY_CODED: 1
    BLE_LL_CFG_FEAT_LL_PRIVACY: 1
    BLE_LL_CFG_FEAT_CTRL_TO_HOST_FLOW_CONTROL: 1
    BLE_LL_CONN_INIT_MAX_TX_BYTES: 251
    BLE_LL_CONN_INIT_SLOTS: 4
    BLE_LL_DTM: 1
    BLE_LL_DTM_EXTENSIONS: 1
    BLE_LL_VND_EVENT_ON_ASSERT: 1
    BLE_MAX_CONNECTIONS: 5
    BLE_EXT_ADV: 1
    BLE_EXT_ADV_MAX_SIZE: 1650
    BLE_MAX_PERIODIC_SYNCS: 5
    BLE_MULTI_ADV_INSTANCES: 5
    BLE_PERIODIC_ADV: 1
    BLE_PERIODIC_ADV_SYNC_TRANSFER: 1

    BLE_VERSION: 54
    BLE_ISO: 1
    BLE_ISO_BROADCASTER: 1
    BLE_MAX_BIG: 1
    BLE_MAX_BIS: 2
